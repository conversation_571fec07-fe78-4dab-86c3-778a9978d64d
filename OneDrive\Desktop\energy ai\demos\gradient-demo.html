<!DOCTYPE html>
<html lang="en" data-theme="gradient">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Gradient Futuristic Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Space Grotesk', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating Orbs */
        .floating-orbs {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .orb {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
            backdrop-filter: blur(10px);
            animation: float 20s infinite linear;
        }

        .orb:nth-child(1) { width: 100px; height: 100px; top: 20%; left: 10%; animation-delay: 0s; }
        .orb:nth-child(2) { width: 150px; height: 150px; top: 60%; left: 80%; animation-delay: 5s; }
        .orb:nth-child(3) { width: 80px; height: 80px; top: 80%; left: 20%; animation-delay: 10s; }
        .orb:nth-child(4) { width: 120px; height: 120px; top: 30%; left: 70%; animation-delay: 15s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-50px) rotate(120deg); }
            66% { transform: translateY(50px) rotate(240deg); }
        }

        /* Glass Morphism */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #f093fb, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            animation: logoSpin 10s linear infinite;
        }

        @keyframes logoSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        .nav-menu a:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        .btn-gradient {
            background: linear-gradient(45deg, #f093fb, #f5576c, #4facfe);
            background-size: 200% 200%;
            animation: gradientMove 3s ease infinite;
            color: white;
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .btn-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f093fb, #667eea, #4facfe);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGradient 5s ease infinite;
            line-height: 1.2;
        }

        @keyframes textGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: linear-gradient(45deg, #fff, #f093fb, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            border-radius: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(240, 147, 251, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            position: relative;
            z-index: 2;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: white;
            position: relative;
            z-index: 2;
        }

        .feature-card p {
            opacity: 0.9;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        /* Animated Section */
        .animated-section {
            padding: 100px 20px;
            text-align: center;
            position: relative;
        }

        .wave-container {
            width: 100%;
            height: 200px;
            position: relative;
            overflow: hidden;
            margin: 40px 0;
        }

        .wave {
            position: absolute;
            top: 0;
            left: 0;
            width: 200%;
            height: 100%;
            background: linear-gradient(90deg, 
                transparent, 
                rgba(102, 126, 234, 0.3), 
                rgba(240, 147, 251, 0.3), 
                rgba(79, 172, 254, 0.3), 
                transparent
            );
            animation: wave 4s ease-in-out infinite;
        }

        .wave:nth-child(2) {
            animation-delay: 1s;
            opacity: 0.7;
        }

        .wave:nth-child(3) {
            animation-delay: 2s;
            opacity: 0.5;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(-50%); }
            50% { transform: translateX(0%); }
        }

        /* Contact Section */
        .contact {
            padding: 100px 20px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            padding: 40px;
            border-radius: 25px;
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Footer */
        .footer {
            padding: 40px 20px;
            text-align: center;
            margin-top: 50px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
            border-radius: 25px;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Particle Effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            animation: particleFloat 15s infinite linear;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Orbs -->
    <div class="floating-orbs">
        <div class="orb"></div>
        <div class="orb"></div>
        <div class="orb"></div>
        <div class="orb"></div>
    </div>

    <!-- Particles -->
    <div class="particles" id="particles"></div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← Back to Gallery</a>

    <!-- Navigation -->
    <nav class="navbar glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#animated">Experience</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="#contact" class="btn btn-gradient">Get Started</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>Future Energy Solutions</h1>
            <p>Experience the next generation of energy management with our stunning gradient-powered interface that brings data to life through beautiful, flowing visualizations.</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">Explore Features</a>
                <a href="#animated" class="btn btn-glass">See Animation</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">Dynamic Energy Features</h2>
        <div class="features-grid">
            <div class="feature-card glass">
                <div class="feature-icon">🌈</div>
                <h3>Gradient Analytics</h3>
                <p>Beautiful data visualization with flowing gradients that make complex energy data intuitive and engaging to understand.</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">⚡</div>
                <h3>Dynamic Monitoring</h3>
                <p>Real-time energy tracking with animated interfaces that respond to your consumption patterns with fluid motion.</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🎨</div>
                <h3>Visual Excellence</h3>
                <p>Stunning gradient-based UI that transforms energy management into a visually captivating and enjoyable experience.</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🚀</div>
                <h3>Future Ready</h3>
                <p>Cutting-edge design that evolves with technology, ensuring your energy interface stays modern and relevant.</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">💫</div>
                <h3>Smooth Interactions</h3>
                <p>Fluid animations and transitions that make every interaction feel natural and responsive to your touch.</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🌟</div>
                <h3>Immersive Experience</h3>
                <p>Engaging visual effects that create an immersive environment for managing your energy consumption efficiently.</p>
            </div>
        </div>
    </section>

    <!-- Animated Section -->
    <section class="animated-section" id="animated">
        <h2 class="section-title">Energy Flow Visualization</h2>
        <p style="opacity: 0.9; margin-bottom: 20px;">Watch how energy flows through our dynamic interface</p>
        
        <div class="wave-container">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="wave"></div>
        </div>
        
        <div style="margin-top: 40px;">
            <button class="btn btn-gradient" onclick="triggerEnergyBurst()">
                Trigger Energy Burst ⚡
            </button>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title">Connect with the Future</h2>
        <p style="opacity: 0.9;">Ready to transform your energy experience with gradient-powered solutions?</p>
        
        <form class="contact-form glass">
            <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" placeholder="Enter your full name" required>
            </div>
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" placeholder="+****************">
            </div>
            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" rows="5" placeholder="Tell us about your energy vision..." required></textarea>
            </div>
            <button type="submit" class="btn btn-gradient" style="width: 100%;">
                Send Message ✨
            </button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content glass">
            <p>&copy; 2024 Energy.AI - Powered by Gradients</p>
            <p style="margin-top: 10px; opacity: 0.7;">Gradient Futuristic Design - Demo Version</p>
        </div>
    </footer>

    <script>
        // Create Particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Energy Burst Effect
        function triggerEnergyBurst() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.textContent = 'Energy Flowing... ⚡';
            button.style.animation = 'gradientMove 0.5s ease infinite';
            
            // Create burst effect
            for (let i = 0; i < 20; i++) {
                const burst = document.createElement('div');
                burst.style.position = 'fixed';
                burst.style.left = '50%';
                burst.style.top = '50%';
                burst.style.width = '4px';
                burst.style.height = '4px';
                burst.style.background = 'linear-gradient(45deg, #667eea, #764ba2, #f093fb)';
                burst.style.borderRadius = '50%';
                burst.style.pointerEvents = 'none';
                burst.style.zIndex = '9999';
                
                const angle = (i / 20) * 360;
                const distance = 200;
                const x = Math.cos(angle * Math.PI / 180) * distance;
                const y = Math.sin(angle * Math.PI / 180) * distance;
                
                burst.style.animation = `burstOut 1s ease-out forwards`;
                burst.style.setProperty('--x', x + 'px');
                burst.style.setProperty('--y', y + 'px');
                
                document.body.appendChild(burst);
                
                setTimeout(() => {
                    burst.remove();
                }, 1000);
            }
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.animation = 'gradientMove 3s ease infinite';
            }, 2000);
        }

        // Add burst animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes burstOut {
                0% {
                    transform: translate(-50%, -50%) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translate(calc(-50% + var(--x)), calc(-50% + var(--y))) scale(0);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'Transmitting... 🚀';
            submitBtn.style.animation = 'gradientMove 0.5s ease infinite';
            
            setTimeout(() => {
                submitBtn.textContent = 'Message Sent! ✅';
                submitBtn.style.background = 'linear-gradient(45deg, #4caf50, #8bc34a)';
                
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.background = 'linear-gradient(45deg, #f093fb, #f5576c, #4facfe)';
                    submitBtn.style.animation = 'gradientMove 3s ease infinite';
                    this.reset();
                }, 2000);
            }, 1500);
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Initialize
        createParticles();

        // Random gradient changes
        setInterval(() => {
            const cards = document.querySelectorAll('.feature-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.style.background = 'linear-gradient(45deg, rgba(102, 126, 234, 0.2), rgba(240, 147, 251, 0.2))';
            setTimeout(() => {
                randomCard.style.background = 'rgba(255, 255, 255, 0.1)';
            }, 2000);
        }, 3000);

        console.log('🌈 Gradient Futuristic Demo Loaded Successfully!');
    </script>
</body>
</html>

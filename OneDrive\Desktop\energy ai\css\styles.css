/* ===== ENERGY.AI MAIN STYLES ===== */

/* ===== CSS VARIABLES ===== */

/* ===== DARK THEME (DEFAULT) ===== */
:root, [data-theme="dark"] {
    /* Primary Colors */
    --primary-color: #1976d2;
    --secondary-color: #ff7200;
    --accent-color: #ff7200; /* Orange for dark theme */

    /* Background Colors */
    --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    --background-secondary: #1a1a1a;
    --background-tertiary: #2a2a2a;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;

    /* Gradients */
    --primary-gradient: linear-gradient(135deg, #1976d2, #42a5f5);
    --secondary-gradient: linear-gradient(135deg, #ff7200, #ffb74d);
    --energy-gradient: linear-gradient(135deg, #1976d2, #ff7200);

    /* Shadows */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-orange: 0 4px 20px rgba(255, 114, 0, 0.3);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Border Radius */
    --border-radius: 8px;
    --border-radius-large: 16px;
}

/* ===== LIGHT THEME ===== */
[data-theme="light"] {
    /* Primary Colors */
    --primary-color: #1976d2;
    --secondary-color: #ff7200;
    --accent-color: #1976d2; /* Blue for light theme */

    /* Background Colors */
    --background-primary: #ffffff;
    --background-secondary: #f8f9fa;
    --background-tertiary: #e9ecef;

    /* Text Colors */
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: all var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: 3.5rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 1.8rem;
}

p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-color);
}

/* Main Layout */
.main {
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-fast);
}

[data-theme="light"] .navbar {
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.icon {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* ===== SITE LOGO ===== */

/* Default/Dark Theme */
.site-logo {
    width: 40px;
    height: 40px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Light Theme */
[data-theme="light"] .site-logo {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.site-logo::before {
    content: "⚡";
    font-size: 1.5rem;
    color: white;
    animation: pulse 2s infinite;
}

/* ===== LOGO TEXT ===== */

/* Default/Dark Theme */
.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--secondary-color); /* Orange for dark theme */
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
}

/* Light Theme */
[data-theme="light"] .logo {
    background: var(--energy-gradient); /* Gradient for light theme */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* Navigation Menu */
.menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.menu a {
    color: var(--text-primary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    position: relative;
}

/* ===== MENU LINK HOVER & ACTIVE STATES ===== */

/* Default/Dark Theme */
.menu a:hover,
.menu a.active {
    color: var(--secondary-color); /* Orange for dark theme */
    background: rgba(255, 114, 0, 0.1);
}

/* Light Theme */
[data-theme="light"] .menu a:hover,
[data-theme="light"] .menu a.active {
    color: var(--primary-color); /* Blue for light theme */
    background: rgba(25, 118, 210, 0.1);
}

/* ===== MENU LINK UNDERLINE EFFECT ===== */

/* Default/Dark Theme */
.menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

/* Light Theme */
[data-theme="light"] .menu a::after {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.menu a:hover::after,
.menu a.active::after {
    width: 80%;
}

/* Language Toggle */
.language-toggle {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

/* ===== LANGUAGE BUTTONS ===== */

/* Default/Dark Theme */
.lang-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.9rem;
}

.lang-btn.active,
.lang-btn:hover {
    background: var(--secondary-color); /* Orange for dark theme */
    color: white;
}

/* Light Theme */
[data-theme="light"] .lang-btn.active,
[data-theme="light"] .lang-btn:hover {
    background: var(--primary-color); /* Blue for light theme */
    color: white;
}

/* Theme Switch */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
}

.theme-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #333;
    transition: var(--transition-normal);
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background: white;
    transition: var(--transition-normal);
    border-radius: 50%;
}

input:checked + .slider {
    background: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(30px);
}

/* Content Section */
/* ===== MAIN CONTENT ===== */
.content {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 2rem 4rem;
    padding-top: 120px;
    position: relative;
    z-index: 10; /* Higher z-index to stay above geometric shapes */
}

.content h1 {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 2rem;
    color: #ffffff;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* ===== DARK THEME CONTENT ===== */
:root .content h1 span,
[data-theme="dark"] .content h1 span {
    display: inline;
    color: #ff7200; /* Orange for dark theme */
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
}

/* ===== LIGHT THEME CONTENT ===== */
[data-theme="light"] .content h1 {
    color: #212529; /* Dark text for light theme */
}

[data-theme="light"] .content h1 span {
    color: #1976d2; /* Blue for light theme */
}

[data-theme="light"] .content .par {
    color: #495057; /* Darker gray for light theme */
}

[data-theme="light"] .par {
    color: #495057; /* Darker gray for light theme */
}

.content .par {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #b0b0b0;
    max-width: 600px;
}

.par {
    font-size: 1.2rem;
    color: #b0b0b0;
    max-width: 600px;
    margin-bottom: 3rem;
    line-height: 1.8;
}

/* ===== MAIN BUTTONS ===== */

/* Default/Dark Theme */
.cn {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

/* Light Theme */
[data-theme="light"] .cn {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.cn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cn:hover::before {
    left: 100%;
}

.cn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* تحسين واجهة الدردشة للوضع الفاتح */
.chat-input button:hover {
    background: rgba(25, 118, 210, 0.5);
}

/* تحسين واجهة الدردشة للوضع الفاتح */
[data-theme="light"] .ai-chat-container {
    background: linear-gradient(to bottom, var(--background-secondary), var(--background-tertiary));
    border: 2px solid rgba(25, 118, 210, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .chat-header {
    background: var(--primary-gradient);
    color: #ffffff;
    border-bottom: 2px solid rgba(25, 118, 210, 0.2);
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-messages {
    background: rgba(255, 255, 255, 0.8);
    background-image:
        linear-gradient(rgba(25, 118, 210, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(25, 118, 210, 0.02) 1px, transparent 1px);
}

[data-theme="light"] .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(25, 118, 210, 0.5);
}

[data-theme="light"] .bot .message-content {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    color: var(--text-primary);
    border: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .user .message-content {
    background: var(--primary-gradient);
    color: #ffffff;
}

[data-theme="light"] .suggested-questions {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), rgba(248, 249, 250, 0.9));
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .question-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    border: 1px solid rgba(25, 118, 210, 0.2);
    color: var(--text-primary);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .question-btn:hover {
    background: var(--primary-gradient);
    color: #ffffff;
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-input {
    background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
    border-top: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .chat-input::before {
    background: linear-gradient(90deg, transparent, rgba(25, 118, 210, 0.2), transparent);
}

[data-theme="light"] .chat-input input {
    border: 2px solid rgba(25, 118, 210, 0.2);
    background: rgba(255, 255, 255, 0.7);
    color: var(--text-primary);
}

[data-theme="light"] .chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.2);
}

[data-theme="light"] .chat-input button {
    background: var(--primary-gradient);
    color: #ffffff;
    box-shadow: 0 3px 10px rgba(25, 118, 210, 0.3);
}

[data-theme="light"] .chat-input button:hover {
    box-shadow: 0 5px 15px rgba(25, 118, 210, 0.4);
}

[data-theme="light"] .typing-indicator {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .typing-indicator::after {
    border-top: 8px solid rgba(255, 255, 255, 0.9);
}



/* ===== GEOMETRIC BACKGROUND ===== */

/* ===== BACKGROUND ELEMENTS Z-INDEX FIX ===== */
/* Ensure ALL background elements stay behind content */
.animated-background,
.background-layer,
.base-layer,
.pulse-effect,
.geometric-shapes,
.floating-shapes,
.main-geometric-shape,
.secondary-shape,
.tertiary-shape,
.quaternary-shape,
.shape,
.shape-1, .shape-2, .shape-3, .shape-4, .shape-5, .shape-6, .shape-7, .shape-8,
.energy-lines,
.energy-line,
.line-1, .line-2, .line-3,
.particles,
.particle,
.particle-1, .particle-2, .particle-3, .particle-4, .particle-5,
.light-effects,
.light-beam,
.beam-1, .beam-2, .beam-3 {
    z-index: 1 !important;
    pointer-events: none !important;
}

/* تحسين الخلفية الهندسية للوضع الفاتح */
[data-theme="light"] .geometric-background {
    background: linear-gradient(135deg,
        #ffffff 0%,
        #f8f9fa 20%,
        #e3f2fd 40%,
        #f1f8e9 60%,
        #fff3e0 80%,
        #ffffff 100%);
}

[data-theme="light"] .main-geometric-shape {
    background: linear-gradient(135deg,
        rgba(25, 118, 210, 0.06) 0%,
        rgba(33, 150, 243, 0.08) 30%,
        rgba(66, 165, 245, 0.05) 60%,
        transparent 100%);
    filter: blur(60px);
}

[data-theme="light"] .secondary-shape {
    background: linear-gradient(45deg,
        rgba(255, 152, 0, 0.04) 0%,
        rgba(255, 193, 7, 0.06) 40%,
        rgba(255, 235, 59, 0.03) 70%,
        transparent 100%);
    filter: blur(50px);
}

[data-theme="light"] .tertiary-shape {
    background: linear-gradient(225deg,
        rgba(76, 175, 80, 0.03) 0%,
        rgba(129, 199, 132, 0.05) 50%,
        transparent 100%);
    filter: blur(70px);
}

[data-theme="light"] .shape {
    background: linear-gradient(45deg,
        rgba(25, 118, 210, 0.08),
        rgba(33, 150, 243, 0.04));
}

[data-theme="light"] .shape-1 {
    background: linear-gradient(135deg,
        rgba(25, 118, 210, 0.12),
        rgba(33, 150, 243, 0.08));
    box-shadow: 0 0 80px rgba(25, 118, 210, 0.1);
}

[data-theme="light"] .shape-2 {
    background: linear-gradient(45deg,
        rgba(255, 152, 0, 0.1),
        rgba(255, 193, 7, 0.06));
    box-shadow: 0 0 60px rgba(255, 152, 0, 0.08);
}

[data-theme="light"] .shape-3 {
    background: linear-gradient(225deg,
        rgba(76, 175, 80, 0.09),
        rgba(129, 199, 132, 0.06));
    box-shadow: 0 0 50px rgba(76, 175, 80, 0.06);
}

[data-theme="light"] .shape-4 {
    background: linear-gradient(315deg,
        rgba(33, 150, 243, 0.08),
        rgba(66, 165, 245, 0.05));
}

[data-theme="light"] .shape-5 {
    background: linear-gradient(180deg,
        rgba(255, 152, 0, 0.1),
        rgba(255, 193, 7, 0.06));
    box-shadow: 0 0 60px rgba(255, 152, 0, 0.08);
}

[data-theme="light"] .particle {
    background: rgba(25, 118, 210, 0.6);
    box-shadow: 0 0 8px rgba(25, 118, 210, 0.4);
}

.api-tester-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--secondary-color);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    z-index: 10;
    position: relative;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.api-icon {
    width: 70px;
    height: 70px;
    transition: all 0.3s ease;
}

.design-image-link:hover .api-tester-icon {
    background: rgba(255, 255, 255, 0.5);
    transform: scale(1.15);
    border-color: var(--secondary-color);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.design-image-link:hover .api-icon {
    transform: scale(1.15);
    filter: brightness(1.2);
}

.api-link-container {
    display: flex;
    justify-content: center;
    margin: 15px 0;
}

.api-tester-link {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 15px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 114, 0, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .api-tester-link {
    color: #42A5F5;
}

.api-link-icon {
    margin-left: 8px;
    font-size: 18px;
}

.api-link-text {
    font-size: 14px;
}

.api-tester-link:hover {
    background: rgba(255, 114, 0, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.design-image::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, var(--card-bg) 0%, transparent 100%);
}

/* ===== DESIGN CARD HEADINGS ===== */

/* Dark Theme */
:root .design-card h3,
[data-theme="dark"] .design-card h3 {
    padding: 20px 20px 10px;
    font-size: 22px;
    color: #ff7200; /* Orange for dark theme */
}

/* Light Theme */
[data-theme="light"] .design-card h3 {
    color: #1976d2; /* Blue for light theme */
}

/* ===== DESIGN CARD TEXT ===== */

/* Dark Theme */
:root .design-card p,
[data-theme="dark"] .design-card p {
    padding: 0 20px 20px;
    color: #b0b0b0; /* Light gray for dark theme */
    font-size: 16px;
    line-height: 1.5;
}

/* Light Theme */
[data-theme="light"] .design-card p {
    color: #495057; /* Darker gray for light theme */
}

/* Contact Section - Enhanced Design */
.contact-section {
    background: linear-gradient(135deg,
        var(--background-primary) 0%,
        var(--background-secondary) 50%,
        var(--background-tertiary) 100%);
    position: relative;
    overflow: hidden;
    padding: 100px 40px;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 30% 20%, rgba(255, 114, 0, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 165, 0, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* ===== CONTACT HEADER ===== */
.contact-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    z-index: 10; /* Higher z-index to stay above geometric shapes */
}

/* ===== CONTACT HEADER ===== */

/* Default/Dark Theme */
.contact-header h2 {
    font-size: 48px;
    font-weight: 700;
    color: #ffffff; /* White for dark theme */
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    margin-bottom: 20px;
    filter: drop-shadow(0 4px 8px var(--shadow-orange));
}

/* Light Theme */
[data-theme="light"] .contact-header h2 {
    background: var(--primary-gradient); /* Gradient for light theme */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* ===== CONTACT SUBTITLE ===== */

/* Dark Theme */
:root .contact-subtitle,
[data-theme="dark"] .contact-subtitle {
    font-size: 20px;
    color: #b0b0b0; /* Light gray for dark theme */
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Light Theme */
[data-theme="light"] .contact-subtitle {
    color: #495057; /* Darker gray for light theme */
}

/* Fixed CTA Button */
.fixed-cta-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 999;
}

/* ===== FIXED CTA BUTTON ===== */

/* Default/Dark Theme */
.fixed-cta-btn {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    box-shadow: var(--shadow-heavy);
    transition: all var(--transition-normal);
    animation: pulse 2s infinite;
}

/* Light Theme */
[data-theme="light"] .fixed-cta-btn {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.fixed-cta-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 40px rgba(255, 114, 0, 0.4);
}

/* ===== SECTIONS ===== */
.section {
    padding: 100px 4rem;
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 10; /* Higher z-index to stay above geometric shapes */
}

/* ===== SECTION HEADINGS ===== */

/* Dark Theme */
:root .section h2,
[data-theme="dark"] .section h2 {
    text-align: center;
    font-size: 3rem;
    margin-bottom: 3rem;
    color: #ffffff;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
}

/* Light Theme */
[data-theme="light"] .section h2 {
    color: #212529; /* Dark text for light theme */
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, var(--background-primary), var(--background-secondary));
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

/* ===== ABOUT CONTENT ===== */

/* Dark Theme */
:root .about-content p,
[data-theme="dark"] .about-content p {
    font-size: 1.2rem;
    color: #b0b0b0; /* Light gray for dark theme */
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Light Theme */
[data-theme="light"] .about-content p {
    color: #495057; /* Darker gray for light theme */
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.feature {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: var(--border-radius-large);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    background: rgba(255, 255, 255, 0.1);
}

/* ===== FEATURE ICONS ===== */

/* Default/Dark Theme */
.feature-icon-wrapper {
    width: 80px;
    height: 80px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

/* Light Theme */
[data-theme="light"] .feature-icon-wrapper {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

/* ===== FEATURE CARDS ===== */

/* Dark Theme */
:root .feature h3,
[data-theme="dark"] .feature h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 1rem;
}

:root .feature p,
[data-theme="dark"] .feature p {
    color: #b0b0b0; /* Light gray for dark theme */
    line-height: 1.6;
}

/* Light Theme */
[data-theme="light"] .feature h3 {
    color: #1976d2; /* Blue for light theme */
}

[data-theme="light"] .feature p {
    color: #495057; /* Darker gray for light theme */
}

/* Service Section */
.service-section {
    background: linear-gradient(135deg, var(--background-secondary), var(--background-tertiary));
}

.service-content {
    max-width: 1200px;
    margin: 0 auto;
}

.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: var(--border-radius-large);
    text-align: center;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    background: rgba(255, 255, 255, 0.1);
}

/* ===== SERVICE ICONS ===== */

/* Default/Dark Theme */
.service-icon {
    width: 70px;
    height: 70px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: white;
}

/* Light Theme */
[data-theme="light"] .service-icon {
    background: var(--secondary-gradient); /* Orange gradient for light theme too */
}

/* ===== SERVICE CARDS ===== */

/* Dark Theme */
:root .service-card h3,
[data-theme="dark"] .service-card h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

:root .service-card p,
[data-theme="dark"] .service-card p {
    color: #b0b0b0; /* Light gray for dark theme */
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Light Theme */
[data-theme="light"] .service-card h3 {
    color: #1976d2; /* Blue for light theme */
}

[data-theme="light"] .service-card p {
    color: #495057; /* Darker gray for light theme */
}

/* Design Section */
.design-section {
    background: linear-gradient(135deg, var(--background-primary), var(--background-secondary));
}

.design-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.design-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.design-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

/* ===== DESIGN IMAGE ===== */

/* Default/Dark Theme */
.design-image {
    height: 250px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    position: relative;
}

/* Light Theme */
[data-theme="light"] .design-image {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.map-card .design-image {
    height: 300px;
    padding: 0;
}

.embedded-map {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.map-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.map-expand-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-fast);
}

.map-expand-btn:hover {
    background: var(--accent-color);
}

/* AI Chat Container */
.ai-chat-container {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 400px;
    max-height: 600px;
    background: rgba(10, 10, 10, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-large);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: var(--shadow-heavy);
    z-index: 998;
    display: none;
    flex-direction: column;
    overflow: hidden;
    transition: all var(--transition-normal);
}

[data-theme="light"] .ai-chat-container {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* ===== CHAT HEADER ===== */

/* Default/Dark Theme */
.chat-header {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

/* Light Theme */
[data-theme="light"] .chat-header {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.chat-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.chat-controls {
    display: flex;
    gap: 0.5rem;
}

.chat-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.chat-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 300px;
    background: rgba(255, 255, 255, 0.02);
}

.message {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    align-items: flex-start;
}

.message.user {
    flex-direction: row-reverse;
}

/* ===== AVATAR ===== */

/* Default/Dark Theme */
.avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Light Theme */
[data-theme="light"] .avatar {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.avatar-icon-wrapper {
    color: white;
    font-size: 1rem;
}

.message-content {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    max-width: 80%;
    font-size: 0.9rem;
    line-height: 1.4;
}

.message.user .message-content {
    background: var(--primary-gradient);
    color: white;
}

.suggested-questions {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.question-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.8rem;
    transition: all var(--transition-fast);
}

.question-btn:hover {
    background: var(--primary-color);
    color: white;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.chat-input input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    outline: none;
    transition: all var(--transition-fast);
}

.chat-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* ===== CHAT INPUT BUTTON ===== */

/* Default/Dark Theme */
.chat-input button {
    background: var(--secondary-color); /* Orange for dark theme */
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-fast);
}

.chat-input button:hover {
    background: var(--accent-color); /* Orange hover for dark theme */
}

/* Light Theme */
[data-theme="light"] .chat-input button {
    background: var(--primary-color); /* Blue for light theme */
}

[data-theme="light"] .chat-input button:hover {
    background: var(--primary-color); /* Blue hover for light theme */
    opacity: 0.9;
}

.typing-indicator {
    display: none;
    padding: 1rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.typing-indicator span {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    margin: 0 2px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Contact Section */
.consultation-highlight {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(255, 114, 0, 0.1));
    border: 1px solid rgba(25, 118, 210, 0.2);
    border-radius: var(--border-radius-large);
    padding: 2rem;
    margin-bottom: 3rem;
    text-align: center;
}

.consultation-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

/* ===== CONSULTATION ICONS ===== */

/* Default/Dark Theme */
.consultation-icon {
    width: 80px;
    height: 80px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
}

/* Light Theme */
[data-theme="light"] .consultation-icon {
    background: var(--secondary-gradient); /* Orange gradient for light theme too */
}

/* ===== CONSULTATION SECTION ===== */

/* Dark Theme */
:root .consultation-text h3,
[data-theme="dark"] .consultation-text h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 1rem;
}

/* Light Theme */
[data-theme="light"] .consultation-text h3 {
    color: #1976d2; /* Blue for light theme */
}

.consultation-benefits {
    list-style: none;
    text-align: left;
}

/* ===== CONSULTATION BENEFITS ===== */

/* Dark Theme */
:root .consultation-benefits li,
[data-theme="dark"] .consultation-benefits li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #b0b0b0; /* Light gray for dark theme */
}

/* Light Theme */
[data-theme="light"] .consultation-benefits li {
    color: #495057; /* Darker gray for light theme */
}

/* Contact Form */
.contact-form-wrapper {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-large);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== CONTACT FORM ===== */

/* Dark Theme */
:root .contact-form h3,
[data-theme="dark"] .contact-form h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 2rem;
    text-align: center;
}

/* Light Theme */
[data-theme="light"] .contact-form h3 {
    color: #1976d2; /* Blue for light theme */
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    outline: none;
    transition: all var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* ===== SUBMIT BUTTON ===== */

/* Default/Dark Theme */
.submit-btn {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-normal);
    width: 100%;
    justify-content: center;
}

/* Light Theme */
[data-theme="light"] .submit-btn {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.form-status {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 500;
}

.form-status.success {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.form-status.error {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Contact Info */
.contact-info-wrapper {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-large);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== CONTACT INFO ===== */

/* Dark Theme */
:root .contact-info h3,
[data-theme="dark"] .contact-info h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 1rem;
}

:root .info-description,
[data-theme="dark"] .info-description {
    color: #b0b0b0; /* Light gray for dark theme */
    margin-bottom: 2rem;
}

/* Light Theme */
[data-theme="light"] .contact-info h3 {
    color: #1976d2; /* Blue for light theme */
}

[data-theme="light"] .info-description {
    color: #495057; /* Darker gray for light theme */
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

/* ===== INFO ICONS ===== */

/* Default/Dark Theme */
.info-icon {
    width: 50px;
    height: 50px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

/* Light Theme */
[data-theme="light"] .info-icon {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

/* ===== INFO CONTENT HEADINGS ===== */

/* Dark Theme */
:root .info-content h4,
[data-theme="dark"] .info-content h4 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 0.5rem;
}

/* Light Theme */
[data-theme="light"] .info-content h4 {
    color: #1976d2; /* Blue for light theme */
}

.info-content p {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

/* ===== INFO CONTENT SPAN ===== */

/* Dark Theme */
:root .info-content span,
[data-theme="dark"] .info-content span {
    color: #b0b0b0; /* Light gray for dark theme */
    font-size: 0.9rem;
}

/* Light Theme */
[data-theme="light"] .info-content span {
    color: #495057; /* Darker gray for light theme */
}

.contact-map {
    margin-top: 2rem;
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* ===== MAP PLACEHOLDER ===== */

/* Default/Dark Theme */
.map-placeholder {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

/* Light Theme */
[data-theme="light"] .map-placeholder {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.map-placeholder ion-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.map-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    margin-top: 1rem;
    transition: all var(--transition-fast);
}

.map-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Footer */
.footer {
    background: var(--background-primary);
    padding: 4rem 2rem 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* ===== FOOTER BRAND ICON ===== */

/* Default/Dark Theme */
.footer-brand-icon {
    width: 50px;
    height: 50px;
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Light Theme */
[data-theme="light"] .footer-brand-icon {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.footer-brand-icon::before {
    content: "⚡";
    font-size: 1.5rem;
    color: white;
}

/* ===== FOOTER LOGO TEXT ===== */

/* Default/Dark Theme */
.footer-logo h2 {
    color: var(--secondary-color); /* Orange for dark theme */
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    margin: 0;
}

/* Light Theme */
[data-theme="light"] .footer-logo h2 {
    background: var(--energy-gradient); /* Gradient for light theme */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* ===== FOOTER LOGO TEXT ===== */

/* Dark Theme */
:root .footer-logo p,
[data-theme="dark"] .footer-logo p {
    color: #b0b0b0; /* Light gray for dark theme */
    margin: 0;
}

/* Light Theme */
[data-theme="light"] .footer-logo p {
    color: #495057; /* Darker gray for light theme */
}

/* ===== FOOTER HEADINGS ===== */

/* Dark Theme */
:root .footer-links h3,
:root .footer-social h3,
:root .footer-newsletter h3,
[data-theme="dark"] .footer-links h3,
[data-theme="dark"] .footer-social h3,
[data-theme="dark"] .footer-newsletter h3 {
    color: #ff7200; /* Orange for dark theme */
    margin-bottom: 1rem;
}

/* Light Theme */
[data-theme="light"] .footer-links h3,
[data-theme="light"] .footer-social h3,
[data-theme="light"] .footer-newsletter h3 {
    color: #1976d2; /* Blue for light theme */
}

.footer-links ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* ===== FOOTER LINKS ===== */

/* Dark Theme */
:root .footer-links a,
[data-theme="dark"] .footer-links a {
    color: #b0b0b0; /* Light gray for dark theme */
    transition: color var(--transition-fast);
}

/* Light Theme */
[data-theme="light"] .footer-links a {
    color: #495057; /* Darker gray for light theme */
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

/* ===== SOCIAL ICONS HOVER ===== */

/* Default/Dark Theme */
.social-icons a:hover {
    background: var(--secondary-color); /* Orange for dark theme */
    color: white;
    transform: translateY(-2px);
}

/* Light Theme */
[data-theme="light"] .social-icons a:hover {
    background: var(--primary-color); /* Blue for light theme */
    color: white;
    transform: translateY(-2px);
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.newsletter-form input {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    outline: none;
}

/* ===== NEWSLETTER BUTTON ===== */

/* Default/Dark Theme */
.newsletter-form button {
    background: var(--secondary-color); /* Orange for dark theme */
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.newsletter-form button:hover {
    background: var(--accent-color); /* Darker orange for dark theme */
}

/* Light Theme */
[data-theme="light"] .newsletter-form button {
    background: var(--primary-color); /* Blue for light theme */
}

[data-theme="light"] .newsletter-form button:hover {
    background: #1565c0; /* Darker blue for light theme */
}

/* ===== FOOTER BOTTOM ===== */

/* Dark Theme */
:root .footer-bottom,
[data-theme="dark"] .footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #b0b0b0; /* Light gray for dark theme */
}

/* Light Theme */
[data-theme="light"] .footer-bottom {
    color: #495057; /* Darker gray for light theme */
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Map Modal */
.map-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.map-modal-content {
    width: 90%;
    height: 90%;
    background: var(--background-primary);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    position: relative;
}

/* ===== MAP MODAL HEADER ===== */

/* Default/Dark Theme */
.map-modal-header {
    background: var(--secondary-gradient); /* Orange gradient for dark theme */
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Light Theme */
[data-theme="light"] .map-modal-header {
    background: var(--primary-gradient); /* Blue gradient for light theme */
}

.map-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all var(--transition-fast);
}

.map-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.full-map {
    width: 100%;
    height: calc(100% - 70px);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content {
        padding: 2rem 3rem;
        padding-top: 120px;
    }

    .section {
        padding: 80px 3rem;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: 1rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .menu ul {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        text-align: center;
    }

    .language-toggle {
        order: -1;
    }

    .content {
        padding: 1rem 2rem;
        padding-top: 140px;
        text-align: center;
    }

    .content h1 {
        font-size: 2.5rem;
    }

    .par {
        font-size: 1rem;
    }

    .section {
        padding: 60px 2rem;
    }

    .section h2 {
        font-size: 2rem;
    }

    .features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .service-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .design-gallery {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .consultation-content {
        flex-direction: column;
        text-align: center;
    }

    .ai-chat-container {
        left: 1rem;
        right: 1rem;
        width: auto;
        bottom: 1rem;
    }

    .fixed-cta-container {
        bottom: 1rem;
        right: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .content h1 {
        font-size: 2rem;
    }

    .section h2 {
        font-size: 1.5rem;
    }

    .navbar {
        padding: 0.5rem;
    }

    .logo {
        font-size: 1.2rem;
    }

    .menu ul {
        gap: 0.5rem;
    }

    .menu a {
        padding: 0.5rem;
        font-size: 0.9rem;
    }

    .ai-chat-container {
        max-height: 400px;
    }

    .chat-messages {
        max-height: 200px;
    }
}

/* RTL Support */
[dir="rtl"] .navbar {
    direction: rtl;
}

[dir="rtl"] .menu ul {
    direction: rtl;
}

[dir="rtl"] .content {
    text-align: right;
}

[dir="rtl"] .message.user {
    flex-direction: row;
}

[dir="rtl"] .message:not(.user) {
    flex-direction: row-reverse;
}

[dir="rtl"] .consultation-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .info-item {
    flex-direction: row-reverse;
    text-align: right;
}

/* Print Styles */
@media print {
    .navbar,
    .fixed-cta-container,
    .ai-chat-container,
    .animated-background {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .section {
        page-break-inside: avoid;
    }
}

/* ===== CONTACT CONTAINER ===== */
.contact-container {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 60px;
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 10; /* Higher z-index to stay above geometric shapes */
}

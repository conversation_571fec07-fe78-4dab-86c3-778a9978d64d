<!DOCTYPE html>
<html lang="en" data-theme="luxury">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Luxury Premium Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: radial-gradient(ellipse at center, #1a1a1a 0%, #0d0d0d 50%, #000000 100%);
            color: #f5f5f5;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Luxury Background */
        .luxury-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(184, 134, 11, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255, 193, 7, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #2d2d2d 100%);
        }

        /* Golden Particles */
        .golden-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 50%;
            animation: luxuryFloat 15s infinite linear;
            box-shadow: 0 0 6px #ffd700;
        }

        @keyframes luxuryFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Luxury Glass */
        .luxury-glass {
            background: linear-gradient(135deg, 
                rgba(255, 215, 0, 0.1) 0%, 
                rgba(0, 0, 0, 0.3) 50%, 
                rgba(184, 134, 11, 0.1) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(255, 215, 0, 0.2),
                0 0 20px rgba(255, 215, 0, 0.1);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffd700, #ffed4e, #b8860b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #ffd700, #b8860b);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #000;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 4px 20px rgba(255, 215, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: luxuryShine 3s ease-in-out infinite;
        }

        @keyframes luxuryShine {
            0%, 100% { transform: rotate(0deg); }
            50% { transform: rotate(180deg); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: #f5f5f5;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        .nav-menu a:hover {
            color: #ffd700;
            background: rgba(255, 215, 0, 0.1);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
        }

        /* Luxury Buttons */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            position: relative;
            overflow: hidden;
        }

        .btn-luxury {
            background: linear-gradient(135deg, #ffd700, #b8860b);
            color: #000;
            box-shadow: 
                0 4px 20px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 215, 0, 0.5);
        }

        .btn-luxury:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 8px 30px rgba(255, 215, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .btn-luxury::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-luxury:hover::before {
            left: 100%;
        }

        .btn-glass-luxury {
            background: rgba(255, 215, 0, 0.1);
            backdrop-filter: blur(10px);
            color: #ffd700;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .btn-glass-luxury:hover {
            background: rgba(255, 215, 0, 0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
        }

        .hero h1 {
            font-family: 'Playfair Display', serif;
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 800;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #ffd700, #ffed4e, #b8860b, #ffd700);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: luxuryGradient 4s ease-in-out infinite;
            line-height: 1.2;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
        }

        @keyframes luxuryGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 40px;
            color: rgba(245, 245, 245, 0.9);
            line-height: 1.6;
            font-weight: 300;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Luxury Decorations */
        .luxury-decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            border: 2px solid rgba(255, 215, 0, 0.1);
            border-radius: 50%;
            animation: luxuryRotate 20s linear infinite;
        }

        .luxury-decoration:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .luxury-decoration:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 5s;
            animation-direction: reverse;
        }

        .luxury-decoration:nth-child(3) {
            bottom: 15%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes luxuryRotate {
            0% { transform: rotate(0deg) scale(1); opacity: 0.3; }
            50% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
            100% { transform: rotate(360deg) scale(1); opacity: 0.3; }
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            font-family: 'Playfair Display', serif;
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #ffd700, #b8860b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(184, 134, 11, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(255, 215, 0, 0.2);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #ffd700, #b8860b);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #000;
            box-shadow: 
                0 10px 30px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: iconShine 2s ease-in-out infinite;
        }

        @keyframes iconShine {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .feature-card h3 {
            font-family: 'Playfair Display', serif;
            font-size: 1.6rem;
            margin-bottom: 15px;
            color: #ffd700;
            font-weight: 600;
        }

        .feature-card p {
            color: rgba(245, 245, 245, 0.8);
            line-height: 1.6;
            font-weight: 300;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(255, 215, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 12px;
            color: #ffd700;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 215, 0, 0.2);
            transform: translateX(-5px);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 2.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Luxury Background -->
    <div class="luxury-bg"></div>
    
    <!-- Golden Particles -->
    <div class="golden-particles" id="particles"></div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة للمعرض</a>

    <!-- Navigation -->
    <nav class="navbar luxury-glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الميزات الفاخرة</a></li>
                <li><a href="#luxury">الفخامة</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <a href="#contact" class="btn btn-luxury">ابدأ التجربة الفاخرة</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="luxury-decoration"></div>
        <div class="luxury-decoration"></div>
        <div class="luxury-decoration"></div>
        
        <div class="hero-content">
            <h1>الطاقة الذكية الفاخرة</h1>
            <p>اكتشف عالم الطاقة المدعوم بالذكاء الاصطناعي مع تجربة فاخرة لا مثيل لها، حيث تلتقي التكنولوجيا المتقدمة بالأناقة المطلقة</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-luxury">استكشف الحلول الفاخرة</a>
                <a href="#contact" class="btn btn-glass-luxury">احجز استشارة VIP</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">ميزات فاخرة حصرية</h2>
        <div class="features-grid">
            <div class="feature-card luxury-glass">
                <div class="feature-icon">👑</div>
                <h3>خدمة VIP حصرية</h3>
                <p>تجربة فاخرة مخصصة مع دعم شخصي على مدار الساعة وحلول طاقة مصممة خصيصاً لاحتياجاتك الفريدة</p>
            </div>
            <div class="feature-card luxury-glass">
                <div class="feature-icon">💎</div>
                <h3>تقنيات متقدمة فاخرة</h3>
                <p>أحدث تقنيات الذكاء الاصطناعي المطورة حصرياً للعملاء المميزين مع واجهات فاخرة وتجربة لا مثيل لها</p>
            </div>
            <div class="feature-card luxury-glass">
                <div class="feature-icon">🏆</div>
                <h3>أداء استثنائي</h3>
                <p>كفاءة طاقة تصل إلى 50% مع ضمان الجودة الذهبية وخدمة ما بعد البيع الفاخرة لسنوات قادمة</p>
            </div>
            <div class="feature-card luxury-glass">
                <div class="feature-icon">🌟</div>
                <h3>تصميم فاخر حصري</h3>
                <p>واجهات مصممة بعناية فائقة مع لمسات ذهبية وتجربة بصرية مذهلة تعكس مستوى الرفاهية والتميز</p>
            </div>
            <div class="feature-card luxury-glass">
                <div class="feature-icon">🔐</div>
                <h3>أمان بمستوى البنوك</h3>
                <p>حماية فائقة الأمان مع تشفير عسكري وأنظمة حماية متعددة الطبقات لضمان خصوصية بياناتك الثمينة</p>
            </div>
            <div class="feature-card luxury-glass">
                <div class="feature-icon">⚡</div>
                <h3>سرعة البرق الذهبية</h3>
                <p>أداء فائق السرعة مع استجابة فورية وتحديثات في الوقت الفعلي لتجربة سلسة ومتميزة</p>
            </div>
        </div>
    </section>

    <script>
        // Create Golden Particles
        function createGoldenParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 15) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Luxury Hover Effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 215, 0, 0.3)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Navbar Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(0, 0, 0, 0.4), rgba(184, 134, 11, 0.15))';
                navbar.style.backdropFilter = 'blur(30px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(0, 0, 0, 0.3), rgba(184, 134, 11, 0.1))';
                navbar.style.backdropFilter = 'blur(20px)';
            }
        });

        // Initialize
        createGoldenParticles();

        console.log('👑✨ Luxury Premium Demo Loaded Successfully! ✨👑');
    </script>
</body>
</html>

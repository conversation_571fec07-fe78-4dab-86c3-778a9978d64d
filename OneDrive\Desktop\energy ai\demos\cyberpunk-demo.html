<!DOCTYPE html>
<html lang="en" data-theme="cyberpunk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Cyberpunk Energy Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #0a0a0a;
            color: #00ff88;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Cyberpunk Grid Background */
        .cyber-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-image: 
                linear-gradient(rgba(0, 255, 136, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 136, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Neon Effects */
        .neon-text {
            text-shadow: 
                0 0 5px currentColor,
                0 0 10px currentColor,
                0 0 15px currentColor,
                0 0 20px currentColor;
        }

        .neon-border {
            border: 2px solid #00ff88;
            box-shadow: 
                0 0 10px #00ff88,
                inset 0 0 10px rgba(0, 255, 136, 0.1);
        }

        .neon-glow {
            box-shadow: 
                0 0 20px #00ff88,
                0 0 40px #00ff88,
                0 0 60px #00ff88;
        }

        /* Glitch Effect */
        .glitch {
            position: relative;
            animation: glitch 2s infinite;
        }

        @keyframes glitch {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #00ff88;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 900;
            color: #00ccff;
            text-transform: uppercase;
            letter-spacing: 3px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: #00ff88;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 10px 15px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            border-color: #00ff88;
            box-shadow: 0 0 15px #00ff88;
            background: rgba(0, 255, 136, 0.1);
        }

        /* Buttons */
        .btn {
            padding: 15px 30px;
            border: 2px solid;
            background: transparent;
            color: inherit;
            font-family: inherit;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            border-color: #00ff88;
            color: #00ff88;
        }

        .btn-primary:hover {
            background: #00ff88;
            color: #000;
            box-shadow: 0 0 30px #00ff88;
        }

        .btn-secondary {
            border-color: #00ccff;
            color: #00ccff;
        }

        .btn-secondary:hover {
            background: #00ccff;
            color: #000;
            box-shadow: 0 0 30px #00ccff;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 800px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 900;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
            background: linear-gradient(45deg, #00ff88, #00ccff, #ff0080);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGlow 3s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from { filter: brightness(1); }
            to { filter: brightness(1.5); }
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            color: #00ccff;
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Floating Elements */
        .floating-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .floating-element {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
            animation: float 10s infinite linear;
        }

        .floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 60%; left: 80%; animation-delay: 2s; background: #00ccff; box-shadow: 0 0 10px #00ccff; }
        .floating-element:nth-child(3) { top: 80%; left: 20%; animation-delay: 4s; background: #ff0080; box-shadow: 0 0 10px #ff0080; }
        .floating-element:nth-child(4) { top: 30%; left: 70%; animation-delay: 6s; }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); opacity: 1; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 0.5; }
            100% { transform: translateY(-200px) rotate(360deg); opacity: 0; }
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 900;
            margin-bottom: 60px;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #00ccff;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff88;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(0, 255, 136, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }

        .feature-card:hover::before {
            opacity: 1;
            animation: scan 1s ease-in-out;
        }

        @keyframes scan {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .feature-card:hover {
            border-color: #00ccff;
            box-shadow: 0 0 30px #00ccff;
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #00ff88;
            filter: drop-shadow(0 0 10px #00ff88);
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #00ccff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .feature-card p {
            color: #00ff88;
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Terminal Section */
        .terminal-section {
            padding: 100px 20px;
            background: rgba(0, 0, 0, 0.9);
            border-top: 2px solid #00ff88;
            border-bottom: 2px solid #00ff88;
        }

        .terminal {
            max-width: 800px;
            margin: 0 auto;
            background: #000;
            border: 2px solid #00ff88;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 30px #00ff88;
        }

        .terminal-header {
            background: #00ff88;
            color: #000;
            padding: 10px 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .terminal-body {
            padding: 20px;
            font-family: 'Courier New', monospace;
            color: #00ff88;
            min-height: 300px;
        }

        .terminal-line {
            margin-bottom: 10px;
            opacity: 0;
            animation: typewriter 0.5s ease-in-out forwards;
        }

        .terminal-line:nth-child(1) { animation-delay: 0.5s; }
        .terminal-line:nth-child(2) { animation-delay: 1s; }
        .terminal-line:nth-child(3) { animation-delay: 1.5s; }
        .terminal-line:nth-child(4) { animation-delay: 2s; }
        .terminal-line:nth-child(5) { animation-delay: 2.5s; }

        @keyframes typewriter {
            to { opacity: 1; }
        }

        .cursor {
            display: inline-block;
            width: 10px;
            height: 20px;
            background: #00ff88;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Contact Section */
        .contact {
            padding: 100px 20px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ff88;
            padding: 40px;
            margin-top: 40px;
            position: relative;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #00ccff;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            background: #000;
            border: 2px solid #00ff88;
            color: #00ff88;
            font-family: inherit;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #00ccff;
            box-shadow: 0 0 20px #00ccff;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(0, 255, 136, 0.5);
        }

        /* Footer */
        .footer {
            padding: 40px 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.9);
            border-top: 2px solid #00ff88;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff88;
            color: #00ff88;
            text-decoration: none;
            font-weight: 700;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #00ff88;
            color: #000;
            box-shadow: 0 0 20px #00ff88;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #000;
        }

        ::-webkit-scrollbar-thumb {
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #00ccff;
            box-shadow: 0 0 10px #00ccff;
        }
    </style>
</head>
<body>
    <!-- Cyberpunk Grid Background -->
    <div class="cyber-grid"></div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← BACK</a>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <div class="logo neon-text glitch">ENERGY.AI</div>
            <ul class="nav-menu">
                <li><a href="#home">HOME</a></li>
                <li><a href="#features">FEATURES</a></li>
                <li><a href="#terminal">SYSTEM</a></li>
                <li><a href="#contact">CONTACT</a></li>
            </ul>
            <a href="#contact" class="btn btn-primary">CONNECT</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="floating-elements">
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
            <div class="floating-element"></div>
        </div>
        <div class="hero-content">
            <h1 class="neon-text glitch">FUTURE ENERGY</h1>
            <p class="neon-text">Advanced AI-powered energy solutions for the digital age. Hack the grid, optimize consumption, dominate efficiency.</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">INITIALIZE SYSTEM</a>
                <a href="#contact" class="btn btn-secondary">REQUEST ACCESS</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title neon-text">SYSTEM MODULES</h2>
        <div class="features-grid">
            <div class="feature-card neon-border">
                <div class="feature-icon">🧠</div>
                <h3>NEURAL NETWORK</h3>
                <p>Advanced AI algorithms analyze energy patterns and predict optimal consumption strategies with 99.9% accuracy.</p>
            </div>
            <div class="feature-card neon-border">
                <div class="feature-icon">⚡</div>
                <h3>QUANTUM PROCESSING</h3>
                <p>Real-time energy monitoring using quantum-enhanced processors for instantaneous optimization and control.</p>
            </div>
            <div class="feature-card neon-border">
                <div class="feature-icon">🔒</div>
                <h3>CYBER SECURITY</h3>
                <p>Military-grade encryption and multi-layer security protocols protect your energy infrastructure from threats.</p>
            </div>
            <div class="feature-card neon-border">
                <div class="feature-icon">🌐</div>
                <h3>GRID INTERFACE</h3>
                <p>Seamless integration with smart grids and IoT devices for comprehensive energy ecosystem management.</p>
            </div>
            <div class="feature-card neon-border">
                <div class="feature-icon">📊</div>
                <h3>DATA MATRIX</h3>
                <p>Advanced analytics dashboard with holographic displays and predictive modeling capabilities.</p>
            </div>
            <div class="feature-card neon-border">
                <div class="feature-icon">🚀</div>
                <h3>TURBO MODE</h3>
                <p>Overclock your energy efficiency with our proprietary acceleration algorithms and boost protocols.</p>
            </div>
        </div>
    </section>

    <!-- Terminal Section -->
    <section class="terminal-section" id="terminal">
        <h2 class="section-title neon-text">SYSTEM STATUS</h2>
        <div class="terminal">
            <div class="terminal-header">
                <span>●</span>
                <span>●</span>
                <span>●</span>
                ENERGY.AI TERMINAL v2.0.1
            </div>
            <div class="terminal-body">
                <div class="terminal-line">$ energy-ai --status</div>
                <div class="terminal-line">[OK] Neural Network: ONLINE</div>
                <div class="terminal-line">[OK] Quantum Processor: ACTIVE</div>
                <div class="terminal-line">[OK] Security Protocols: ENABLED</div>
                <div class="terminal-line">[OK] Grid Interface: CONNECTED</div>
                <div class="terminal-line">$ energy-ai --optimize --target=maximum</div>
                <div class="terminal-line">[INFO] Optimization initiated...</div>
                <div class="terminal-line">[SUCCESS] Energy efficiency increased by 35%</div>
                <div class="terminal-line">[SUCCESS] Cost reduction: $2,847/month</div>
                <div class="terminal-line">$ <span class="cursor"></span></div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title neon-text">ESTABLISH CONNECTION</h2>
        <p class="neon-text" style="color: #00ccff;">Ready to join the energy revolution? Initialize contact protocol and connect with our cyber specialists.</p>
        
        <form class="contact-form">
            <div class="form-group">
                <label for="name">USER ID</label>
                <input type="text" id="name" placeholder="Enter your designation" required>
            </div>
            <div class="form-group">
                <label for="email">COMM CHANNEL</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="phone">SECURE LINE</label>
                <input type="tel" id="phone" placeholder="+XXX XXX XXX XXX">
            </div>
            <div class="form-group">
                <label for="message">TRANSMISSION</label>
                <textarea id="message" rows="5" placeholder="Encode your message..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                TRANSMIT DATA
            </button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <p class="neon-text">&copy; 2024 ENERGY.AI - ALL SYSTEMS OPERATIONAL</p>
        <p style="margin-top: 10px; color: #00ccff;">CYBERPUNK ENERGY INTERFACE - DEMO VERSION</p>
    </footer>

    <script>
        // Glitch Effect on Logo
        setInterval(() => {
            const logo = document.querySelector('.logo');
            logo.style.textShadow = `
                ${Math.random() * 10}px ${Math.random() * 10}px 0 #ff0080,
                ${Math.random() * 10}px ${Math.random() * 10}px 0 #00ccff
            `;
            setTimeout(() => {
                logo.style.textShadow = '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor, 0 0 20px currentColor';
            }, 100);
        }, 3000);

        // Terminal Animation
        function animateTerminal() {
            const lines = document.querySelectorAll('.terminal-line');
            lines.forEach((line, index) => {
                setTimeout(() => {
                    line.style.opacity = '1';
                    if (line.textContent.includes('$')) {
                        typeWriter(line, line.textContent, 50);
                    }
                }, index * 500);
            });
        }

        function typeWriter(element, text, speed) {
            element.textContent = '';
            let i = 0;
            const timer = setInterval(() => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                } else {
                    clearInterval(timer);
                }
            }, speed);
        }

        // Intersection Observer for Terminal
        const terminalObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateTerminal();
                    terminalObserver.unobserve(entry.target);
                }
            });
        });

        const terminalSection = document.querySelector('.terminal-section');
        if (terminalSection) {
            terminalObserver.observe(terminalSection);
        }

        // Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'TRANSMITTING...';
            submitBtn.style.borderColor = '#00ccff';
            submitBtn.style.color = '#00ccff';
            
            setTimeout(() => {
                submitBtn.textContent = 'TRANSMISSION COMPLETE';
                submitBtn.style.borderColor = '#00ff88';
                submitBtn.style.color = '#00ff88';
                
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.borderColor = '#00ff88';
                    submitBtn.style.color = '#00ff88';
                    this.reset();
                }, 2000);
            }, 2000);
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Random Glitch Effects
        setInterval(() => {
            const elements = document.querySelectorAll('.neon-text');
            const randomElement = elements[Math.floor(Math.random() * elements.length)];
            randomElement.style.animation = 'glitch 0.3s ease-in-out';
            setTimeout(() => {
                randomElement.style.animation = '';
            }, 300);
        }, 5000);

        console.log('⚡ CYBERPUNK ENERGY SYSTEM INITIALIZED ⚡');
    </script>
</body>
</html>

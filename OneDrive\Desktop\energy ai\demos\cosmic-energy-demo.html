<!DOCTYPE html>
<html lang="en" data-theme="cosmic">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Cosmic Energy Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;600;700;800;900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Exo 2', sans-serif;
            background: #000011;
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Cosmic Background */
        .cosmic-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(ellipse at 20% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 80%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
                radial-gradient(ellipse at 40% 60%, rgba(120, 198, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #000011 0%, #1a0033 25%, #330066 50%, #0d1b2a 75%, #000011 100%);
        }

        /* Starfield */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s ease-in-out infinite alternate;
        }

        .star:nth-child(odd) {
            animation-delay: 1s;
        }

        .star:nth-child(3n) {
            animation-delay: 2s;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; transform: scale(1); }
            100% { opacity: 1; transform: scale(1.2); }
        }

        /* Cosmic Particles */
        .cosmic-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .cosmic-particle {
            position: absolute;
            border-radius: 50%;
            animation: cosmicFloat 20s infinite linear;
        }

        .cosmic-particle:nth-child(1) {
            width: 4px;
            height: 4px;
            background: radial-gradient(circle, #7877c6, #4c4b9f);
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .cosmic-particle:nth-child(2) {
            width: 6px;
            height: 6px;
            background: radial-gradient(circle, #ff77c6, #cc4499);
            top: 60%;
            left: 80%;
            animation-delay: 5s;
        }

        .cosmic-particle:nth-child(3) {
            width: 5px;
            height: 5px;
            background: radial-gradient(circle, #77c6ff, #4499cc);
            top: 80%;
            left: 20%;
            animation-delay: 10s;
        }

        .cosmic-particle:nth-child(4) {
            width: 3px;
            height: 3px;
            background: radial-gradient(circle, #c677ff, #9944cc);
            top: 30%;
            left: 70%;
            animation-delay: 15s;
        }

        @keyframes cosmicFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            25% { 
                transform: translateY(-100px) rotate(90deg) scale(1.5);
                opacity: 1;
            }
            50% { 
                transform: translateY(-50px) rotate(180deg) scale(0.8);
                opacity: 0.5;
            }
            75% { 
                transform: translateY(-150px) rotate(270deg) scale(1.2);
                opacity: 0.9;
            }
        }

        /* Cosmic Glass */
        .cosmic-glass {
            background: linear-gradient(135deg, 
                rgba(120, 119, 198, 0.1) 0%, 
                rgba(0, 0, 17, 0.3) 50%, 
                rgba(255, 119, 198, 0.1) 100%);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.5),
                inset 0 1px 0 rgba(120, 119, 198, 0.2),
                0 0 20px rgba(120, 119, 198, 0.1);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #7877c6, #ff77c6, #77c6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
            text-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #7877c6, #ff77c6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 0 20px rgba(120, 119, 198, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            animation: cosmicPulse 3s ease-in-out infinite;
        }

        @keyframes cosmicPulse {
            0%, 100% { 
                box-shadow: 
                    0 0 20px rgba(120, 119, 198, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% { 
                box-shadow: 
                    0 0 40px rgba(120, 119, 198, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: cosmicSpin 4s linear infinite;
        }

        @keyframes cosmicSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        .nav-menu a:hover {
            color: #7877c6;
            background: rgba(120, 119, 198, 0.1);
            box-shadow: 0 0 15px rgba(120, 119, 198, 0.3);
        }

        /* Cosmic Buttons */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            position: relative;
            overflow: hidden;
        }

        .btn-cosmic {
            background: linear-gradient(135deg, #7877c6, #ff77c6);
            color: white;
            box-shadow: 
                0 4px 20px rgba(120, 119, 198, 0.4),
                0 0 20px rgba(120, 119, 198, 0.2);
            border: 1px solid rgba(120, 119, 198, 0.5);
        }

        .btn-cosmic:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 8px 30px rgba(120, 119, 198, 0.6),
                0 0 30px rgba(120, 119, 198, 0.4);
        }

        .btn-cosmic::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-cosmic:hover::before {
            left: 100%;
        }

        .btn-glass-cosmic {
            background: rgba(120, 119, 198, 0.1);
            backdrop-filter: blur(10px);
            color: #7877c6;
            border: 1px solid rgba(120, 119, 198, 0.3);
        }

        .btn-glass-cosmic:hover {
            background: rgba(120, 119, 198, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 0 20px rgba(120, 119, 198, 0.3);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 900;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #7877c6, #ff77c6, #77c6ff, #c677ff);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: cosmicGradient 6s ease-in-out infinite;
            line-height: 1.2;
            text-shadow: 0 0 30px rgba(120, 119, 198, 0.5);
        }

        @keyframes cosmicGradient {
            0%, 100% { background-position: 0% 50%; }
            25% { background-position: 100% 0%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 40px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
            font-weight: 300;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Cosmic Orbs */
        .cosmic-orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(120, 119, 198, 0.8), rgba(120, 119, 198, 0.2));
            animation: cosmicOrbit 15s linear infinite;
            box-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
        }

        .cosmic-orb:nth-child(1) {
            width: 150px;
            height: 150px;
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .cosmic-orb:nth-child(2) {
            width: 100px;
            height: 100px;
            top: 70%;
            right: 15%;
            animation-delay: 5s;
            background: radial-gradient(circle at 30% 30%, rgba(255, 119, 198, 0.8), rgba(255, 119, 198, 0.2));
            box-shadow: 0 0 20px rgba(255, 119, 198, 0.5);
        }

        .cosmic-orb:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 20%;
            animation-delay: 10s;
            background: radial-gradient(circle at 30% 30%, rgba(119, 198, 255, 0.8), rgba(119, 198, 255, 0.2));
            box-shadow: 0 0 20px rgba(119, 198, 255, 0.5);
        }

        @keyframes cosmicOrbit {
            0% { transform: rotate(0deg) translateX(50px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #7877c6, #ff77c6, #77c6ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(120, 119, 198, 0.5);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.05), rgba(255, 119, 198, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(120, 119, 198, 0.3);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #7877c6, #ff77c6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 
                0 0 30px rgba(120, 119, 198, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            animation: cosmicIconPulse 4s ease-in-out infinite;
        }

        @keyframes cosmicIconPulse {
            0%, 100% { 
                box-shadow: 
                    0 0 30px rgba(120, 119, 198, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
            50% { 
                box-shadow: 
                    0 0 50px rgba(120, 119, 198, 0.8),
                    inset 0 1px 0 rgba(255, 255, 255, 0.5);
            }
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: cosmicIconSpin 3s linear infinite;
        }

        @keyframes cosmicIconSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-card h3 {
            font-size: 1.6rem;
            margin-bottom: 15px;
            color: #7877c6;
            font-weight: 700;
        }

        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            font-weight: 300;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(120, 119, 198, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 12px;
            color: #7877c6;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(120, 119, 198, 0.2);
            transform: translateX(-5px);
            box-shadow: 0 0 15px rgba(120, 119, 198, 0.3);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 2.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Cosmic Background -->
    <div class="cosmic-bg"></div>
    
    <!-- Starfield -->
    <div class="starfield" id="starfield"></div>
    
    <!-- Cosmic Particles -->
    <div class="cosmic-particles">
        <div class="cosmic-particle"></div>
        <div class="cosmic-particle"></div>
        <div class="cosmic-particle"></div>
        <div class="cosmic-particle"></div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة للمجرة</a>

    <!-- Navigation -->
    <nav class="navbar cosmic-glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">🌌</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الطاقة الكونية</a></li>
                <li><a href="#cosmic">المجرة</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <a href="#contact" class="btn btn-cosmic">ابدأ الرحلة الكونية</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="cosmic-orb"></div>
        <div class="cosmic-orb"></div>
        <div class="cosmic-orb"></div>
        
        <div class="hero-content">
            <h1>الطاقة الكونية اللامحدودة</h1>
            <p>انطلق في رحلة عبر الكون مع تقنيات الطاقة المتقدمة المدعومة بالذكاء الاصطناعي، حيث تلتقي قوى الكون بالتكنولوجيا المستقبلية</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-cosmic">استكشف الطاقة الكونية</a>
                <a href="#contact" class="btn btn-glass-cosmic">انضم للمجرة</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">قوى كونية متقدمة</h2>
        <div class="features-grid">
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">🌟</div>
                <h3>طاقة النجوم</h3>
                <p>استخدم قوة النجوم اللامحدودة لتشغيل أنظمة الطاقة المتقدمة مع تقنيات مستوحاة من الكون الفسيح</p>
            </div>
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">🌌</div>
                <h3>ذكاء مجري</h3>
                <p>خوارزميات ذكية مستوحاة من حركة المجرات لتحليل وتحسين استهلاك الطاقة بطرق لم تكن ممكنة من قبل</p>
            </div>
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">⭐</div>
                <h3>تحكم كوكبي</h3>
                <p>تحكم في أنظمة الطاقة من أي مكان في الكون مع واجهات مستقبلية تحاكي تقنيات الحضارات المتقدمة</p>
            </div>
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">🚀</div>
                <h3>سرعة الضوء</h3>
                <p>معالجة فائقة السرعة تحاكي سرعة الضوء لاستجابة فورية وتحديثات في الوقت الفعلي عبر الشبكة الكونية</p>
            </div>
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">🌠</div>
                <h3>حماية مجرية</h3>
                <p>أنظمة حماية متقدمة مستوحاة من الدروع الكونية لحماية بياناتك من أي تهديدات في الفضاء السيبراني</p>
            </div>
            <div class="feature-card cosmic-glass">
                <div class="feature-icon">🛸</div>
                <h3>تقنيات مستقبلية</h3>
                <p>استخدم أحدث التقنيات المستقبلية المطورة بالتعاون مع أذكى العقول في المجرة لتجربة لا مثيل لها</p>
            </div>
        </div>
    </section>

    <script>
        // Create Starfield
        function createStarfield() {
            const starfield = document.getElementById('starfield');
            const starCount = 100;
            
            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                
                const size = Math.random() * 3 + 1;
                star.style.width = size + 'px';
                star.style.height = size + 'px';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 3 + 2) + 's';
                
                starfield.appendChild(star);
            }
        }

        // Cosmic Hover Effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(120, 119, 198, 0.4)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Navbar Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'linear-gradient(135deg, rgba(120, 119, 198, 0.15), rgba(0, 0, 17, 0.4), rgba(255, 119, 198, 0.15))';
                navbar.style.backdropFilter = 'blur(30px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(0, 0, 17, 0.3), rgba(255, 119, 198, 0.1))';
                navbar.style.backdropFilter = 'blur(25px)';
            }
        });

        // Initialize
        createStarfield();

        console.log('🌌✨ Cosmic Energy Demo Loaded Successfully! ✨🌌');
    </script>
</body>
</html>

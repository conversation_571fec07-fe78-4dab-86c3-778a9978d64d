<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Enhanced Modern Glassmorphism Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            background-attachment: fixed;
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Enhanced Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }
        .shape:nth-child(5) { width: 60px; height: 60px; top: 45%; left: 45%; animation-delay: 12s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }

        /* Enhanced Glass Components */
        .glass {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 12px 40px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
        }

        /* Compact Navigation */
        .navbar {
            position: fixed;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 10px 25px;
            width: 95%;
            max-width: 1100px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 50px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3rem;
            font-weight: 700;
            color: white;
        }

        .logo-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: logoShine 4s ease-in-out infinite;
        }

        @keyframes logoShine {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 25px;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            padding: 8px 15px;
            border-radius: 10px;
            position: relative;
        }

        .nav-menu a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
        }

        .nav-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* Compact Theme Toggle */
        .theme-toggle {
            width: 45px;
            height: 22px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 22px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .theme-toggle.active {
            background: linear-gradient(135deg, #ff7200, #ff9500);
        }

        .theme-toggle.active::before {
            transform: translateX(23px);
        }

        /* Enhanced Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff7200, #ff9500);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 114, 0, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(255, 114, 0, 0.35);
        }

        .btn-glass {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        /* Compact Hero Section */
        .hero {
            min-height: 85vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 80px 20px 0;
            position: relative;
        }

        .hero-content {
            max-width: 750px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.2rem, 4.5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 18px;
            background: linear-gradient(135deg, #ffffff, #ff7200, #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
            animation: textGlow 4s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from { filter: brightness(1) contrast(1); }
            to { filter: brightness(1.1) contrast(1.1); }
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 35px;
            opacity: 0.9;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.85);
        }

        .hero-actions {
            display: flex;
            gap: 18px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Enhanced Features Section */
        .features {
            padding: 80px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 50px;
            background: linear-gradient(135deg, #ffffff, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .feature-card {
            padding: 35px 25px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 114, 0, 0.08), transparent);
            transition: left 0.6s;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(255, 114, 0, 0.1);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            margin: 0 auto 18px;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(255, 114, 0, 0.25);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .feature-card:hover .feature-icon::before {
            transform: translateX(100%);
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 12px;
            color: white;
            font-weight: 600;
        }

        .feature-card p {
            opacity: 0.85;
            line-height: 1.6;
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Enhanced Stats Section */
        .stats {
            padding: 70px 20px;
            text-align: center;
        }

        .stats-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 50px 35px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 35px;
            margin-top: 35px;
        }

        .stat-item {
            text-align: center;
            position: relative;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff7200, #1976d2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            position: relative;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Enhanced Contact Section */
        .contact {
            padding: 80px 20px;
            max-width: 750px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            padding: 35px;
            margin-top: 35px;
        }

        .form-group {
            margin-bottom: 22px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 18px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            color: white;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ff7200;
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 20px rgba(255, 114, 0, 0.15);
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* Enhanced Footer */
        .footer {
            padding: 35px 20px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.08);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 25px;
        }

        .footer p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-3px);
        }

        /* Enhanced Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero {
                min-height: 80vh;
                padding: 70px 15px 0;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }
            
            .navbar {
                width: 98%;
                padding: 8px 20px;
            }
            
            .nav-content {
                height: 45px;
            }
        }

        @media (max-width: 480px) {
            .features {
                padding: 60px 15px;
            }
            
            .contact {
                padding: 60px 15px;
            }
            
            .stats {
                padding: 50px 15px;
            }
        }

        /* Enhanced Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        /* Improved Scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #ff7200, #1976d2);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #ff9500, #42a5f5);
        }
    </style>
</head>
<body>
    <!-- Enhanced Background Animation -->
    <div class="bg-animation">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة</a>

    <!-- Compact Navigation -->
    <nav class="navbar glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الميزات</a></li>
                <li><a href="#stats">الإحصائيات</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <div class="nav-actions">
                <div class="theme-toggle" onclick="toggleTheme()"></div>
                <a href="#contact" class="btn btn-primary">ابدأ الآن</a>
            </div>
        </div>
    </nav>

    <!-- Enhanced Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1>مستقبل الطاقة الذكية المحسن</h1>
            <p>اكتشف حلول الطاقة المدعومة بالذكاء الاصطناعي مع تصميم glassmorphism محسن وهيدر مدمج أنيق</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">استكشف الحلول المحسنة</a>
                <a href="#contact" class="btn btn-glass">احجز استشارة مجانية</a>
            </div>
        </div>
    </section>

    <!-- Enhanced Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">ميزات محسنة ومتطورة</h2>
        <div class="features-grid">
            <div class="feature-card glass">
                <div class="feature-icon">🧠</div>
                <h3>ذكاء اصطناعي متقدم</h3>
                <p>خوارزميات تعلم آلي محسنة تحلل أنماط استهلاك الطاقة وتقدم توصيات ذكية مع واجهة أكثر أناقة</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">📊</div>
                <h3>تحليلات في الوقت الفعلي</h3>
                <p>مراقبة مستمرة محسنة لاستهلاك الطاقة مع تقارير تفصيلية وتنبيهات فورية في تصميم مدمج</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🌱</div>
                <h3>حلول مستدامة محسنة</h3>
                <p>تقنيات صديقة للبيئة محسنة تقلل البصمة الكربونية مع واجهة مستخدم أكثر سلاسة وأناقة</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">💰</div>
                <h3>توفير محسن في التكاليف</h3>
                <p>تحسين استهلاك الطاقة المحدث يؤدي إلى توفير يصل إلى 35% من فواتير الكهرباء الشهرية</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">🔒</div>
                <h3>أمان متقدم ومحسن</h3>
                <p>حماية عالية المستوى محسنة لبيانات الطاقة مع تشفير متقدم وأنظمة أمان متعددة الطبقات</p>
            </div>
            <div class="feature-card glass">
                <div class="feature-icon">📱</div>
                <h3>تحكم ذكي مدمج</h3>
                <p>تطبيق موبايل محسن للتحكم في جميع أنظمة الطاقة عن بُعد مع واجهة مدمجة وسهلة الاستخدام</p>
            </div>
        </div>
    </section>

    <!-- Enhanced Stats Section -->
    <section class="stats" id="stats">
        <div class="stats-container glass-strong">
            <h2 class="section-title">أرقام محسنة تتحدث عن نفسها</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">750+</div>
                    <div class="stat-label">عميل راضٍ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">35%</div>
                    <div class="stat-label">توفير في الطاقة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">دعم فني محسن</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">وقت تشغيل</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title">ابدأ رحلتك المحسنة معنا</h2>
        <p>احصل على استشارة مجانية واكتشف كيف يمكن لحلولنا المحسنة تحسين كفاءة الطاقة لديك</p>

        <form class="contact-form glass">
            <div class="form-group">
                <label for="name">الاسم الكامل</label>
                <input type="text" id="name" placeholder="أدخل اسمك الكامل" required>
            </div>
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="phone">رقم الهاتف</label>
                <input type="tel" id="phone" placeholder="+962 79 XXX XXXX">
            </div>
            <div class="form-group">
                <label for="message">الرسالة</label>
                <textarea id="message" rows="4" placeholder="أخبرنا عن احتياجاتك في مجال الطاقة..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                إرسال الطلب المحسن ✨
            </button>
        </form>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer">
        <div class="footer-content glass">
            <p>&copy; 2024 Energy.AI - جميع الحقوق محفوظة</p>
            <p style="margin-top: 8px; opacity: 0.7;">Enhanced Modern Glassmorphism - نسخة محسنة مع هيدر مدمج</p>
        </div>
    </footer>

    <script>
        // Enhanced Theme Toggle
        function toggleTheme() {
            const toggle = document.querySelector('.theme-toggle');
            const body = document.body;

            toggle.classList.toggle('active');

            if (toggle.classList.contains('active')) {
                body.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 25%, #dee2e6 50%, #ced4da 75%, #adb5bd 100%)';
                document.documentElement.style.setProperty('--text-color', '#333');
                // Update glass effects for light mode
                document.querySelectorAll('.glass').forEach(el => {
                    el.style.background = 'rgba(255, 255, 255, 0.25)';
                    el.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                });
            } else {
                body.style.background = 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%)';
                document.documentElement.style.setProperty('--text-color', '#fff');
                // Restore dark mode glass effects
                document.querySelectorAll('.glass').forEach(el => {
                    el.style.background = 'rgba(255, 255, 255, 0.08)';
                    el.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                });
            }
        }

        // Enhanced Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = 'جاري الإرسال المحسن... ⏳';
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.8';

            setTimeout(() => {
                submitBtn.innerHTML = 'تم الإرسال بنجاح! ✅';
                submitBtn.style.background = 'linear-gradient(135deg, #4caf50, #8bc34a)';
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '1';
                    submitBtn.style.background = 'linear-gradient(135deg, #ff7200, #ff9500)';
                    this.reset();
                }, 2000);
            }, 2000);
        });

        // Enhanced Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.12)';
                navbar.style.backdropFilter = 'blur(35px)';
                navbar.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.08)';
                navbar.style.backdropFilter = 'blur(25px)';
                navbar.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
            }
        });

        // Enhanced Stats Animation
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/\D/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');

                        let currentValue = 0;
                        const increment = numericValue / 60;

                        const timer = setInterval(() => {
                            currentValue += increment;
                            if (currentValue >= numericValue) {
                                stat.textContent = finalValue;
                                clearInterval(timer);
                            } else {
                                stat.textContent = Math.floor(currentValue) + suffix;
                            }
                        }, 25);
                    });
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        const statsSection = document.querySelector('.stats');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Enhanced Feature Cards Animation
        const featureObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-on-scroll');
                    featureObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.feature-card').forEach(card => {
            featureObserver.observe(card);
        });

        // Enhanced Random Glow Effects
        setInterval(() => {
            const cards = document.querySelectorAll('.feature-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.style.boxShadow = '0 20px 40px rgba(255, 114, 0, 0.2), 0 0 0 1px rgba(255, 114, 0, 0.15)';
            setTimeout(() => {
                randomCard.style.boxShadow = '';
            }, 2000);
        }, 4000);

        console.log('🔮✨ Enhanced Modern Glassmorphism Demo Loaded Successfully!');
    </script>
</body>
</html>

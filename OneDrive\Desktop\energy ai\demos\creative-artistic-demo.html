<!DOCTYPE html>
<html lang="en" data-theme="creative-artistic">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Creative Artistic Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Comfortaa:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comfortaa', cursive;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
            background-size: 400% 400%;
            animation: artisticGradient 15s ease infinite;
            color: #2c3e50;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        @keyframes artisticGradient {
            0% { background-position: 0% 50%; }
            25% { background-position: 100% 0%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
            100% { background-position: 0% 50%; }
        }

        /* Artistic Background */
        .artistic-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 107, 107, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(78, 205, 196, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(69, 183, 209, 0.2) 0%, transparent 50%);
        }

        /* Creative Shapes */
        .creative-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .creative-shape {
            position: absolute;
            animation: creativeFloat 20s ease-in-out infinite;
        }

        .creative-shape:nth-child(1) {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #ff9ff3);
            border-radius: 50% 20% 50% 20%;
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .creative-shape:nth-child(2) {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            top: 65%;
            right: 15%;
            animation-delay: 5s;
        }

        .creative-shape:nth-child(3) {
            width: 120px;
            height: 60px;
            background: linear-gradient(45deg, #96ceb4, #feca57);
            border-radius: 50px;
            bottom: 20%;
            left: 20%;
            animation-delay: 10s;
        }

        .creative-shape:nth-child(4) {
            width: 90px;
            height: 90px;
            background: linear-gradient(45deg, #54a0ff, #ff9ff3);
            transform: rotate(45deg);
            top: 30%;
            right: 30%;
            animation-delay: 15s;
        }

        @keyframes creativeFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            25% { 
                transform: translateY(-40px) rotate(90deg) scale(1.2);
                opacity: 0.8;
            }
            50% { 
                transform: translateY(-20px) rotate(180deg) scale(0.8);
                opacity: 0.4;
            }
            75% { 
                transform: translateY(-60px) rotate(270deg) scale(1.1);
                opacity: 0.9;
            }
        }

        /* Artistic Glass */
        .artistic-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.3) 0%, 
                rgba(255, 255, 255, 0.1) 50%, 
                rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 30px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 20px 35px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Fredoka', cursive;
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
            animation: logoColorShift 3s ease-in-out infinite;
        }

        @keyframes logoColorShift {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(180deg); }
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 300%;
            animation: iconGradient 4s ease infinite;
            border-radius: 50% 20% 50% 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        @keyframes iconGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: creativeSpin 6s linear infinite;
        }

        @keyframes creativeSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        .nav-menu a:hover {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Creative Buttons */
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
            font-family: 'Fredoka', cursive;
        }

        .btn-creative {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 300% 300%;
            animation: btnGradient 3s ease infinite;
            color: white;
            box-shadow: 
                0 4px 20px rgba(0, 0, 0, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }

        @keyframes btnGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .btn-creative:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 
                0 8px 30px rgba(0, 0, 0, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }

        .btn-creative::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .btn-creative:hover::before {
            left: 100%;
        }

        .btn-artistic {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            color: #2c3e50;
            border: 3px solid rgba(255, 107, 107, 0.4);
        }

        .btn-artistic:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px) rotate(2deg);
            border-color: rgba(78, 205, 196, 0.6);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
        }

        .hero h1 {
            font-family: 'Fredoka', cursive;
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 700;
            margin-bottom: 25px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGradient 5s ease infinite;
            line-height: 1.2;
            transform: rotate(-2deg);
        }

        @keyframes textGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            color: #2c3e50;
            line-height: 1.6;
            font-weight: 500;
            transform: rotate(1deg);
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            font-family: 'Fredoka', cursive;
            text-align: center;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transform: rotate(-1deg);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
            transform: rotate(1deg);
        }

        .feature-card:nth-child(even) {
            transform: rotate(-1deg);
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.05) rotate(0deg);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.2),
                0 0 0 3px rgba(255, 107, 107, 0.3);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 300% 300%;
            animation: iconColorShift 4s ease infinite;
            border-radius: 50% 20% 50% 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 
                0 8px 25px rgba(0, 0, 0, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        @keyframes iconColorShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: iconSpin 8s linear infinite;
        }

        @keyframes iconSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-card h3 {
            font-family: 'Fredoka', cursive;
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 700;
        }

        .feature-card p {
            color: #34495e;
            line-height: 1.6;
            font-weight: 500;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 25px;
            left: 25px;
            z-index: 1001;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            border: 3px solid rgba(255, 107, 107, 0.4);
            border-radius: 25px;
            color: #2c3e50;
            text-decoration: none;
            font-weight: 700;
            font-family: 'Fredoka', cursive;
            transition: all 0.3s ease;
            transform: rotate(-2deg);
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateX(-5px) rotate(0deg);
            border-color: rgba(78, 205, 196, 0.6);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 2.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Creative Animations */
        .bounce {
            animation: creativeBounce 2s ease-in-out infinite;
        }

        @keyframes creativeBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .wiggle {
            animation: creativeWiggle 3s ease-in-out infinite;
        }

        @keyframes creativeWiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(2deg); }
            75% { transform: rotate(-2deg); }
        }
    </style>
</head>
<body>
    <!-- Artistic Background -->
    <div class="artistic-bg"></div>
    
    <!-- Creative Shapes -->
    <div class="creative-shapes">
        <div class="creative-shape"></div>
        <div class="creative-shape"></div>
        <div class="creative-shape"></div>
        <div class="creative-shape"></div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← عودة إبداعية</a>

    <!-- Navigation -->
    <nav class="navbar artistic-glass">
        <div class="nav-content">
            <div class="logo wiggle">
                <div class="logo-icon">🎨</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الإبداع</a></li>
                <li><a href="#art">الفن</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <a href="#contact" class="btn btn-creative bounce">ابدأ الإبداع</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <h1 class="wiggle">الطاقة الذكية بلمسة إبداعية</h1>
            <p class="bounce">اكتشف عالم الطاقة المدعوم بالذكاء الاصطناعي مع تصميم فني إبداعي يجمع بين الألوان الزاهية والحركات المرحة لتجربة لا تُنسى</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-creative">استكشف الإبداع</a>
                <a href="#contact" class="btn btn-artistic">انضم للفن</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title wiggle">ميزات فنية إبداعية</h2>
        <div class="features-grid">
            <div class="feature-card artistic-glass">
                <div class="feature-icon">🎨</div>
                <h3>تصميم فني</h3>
                <p>واجهات مصممة بروح فنية إبداعية مع ألوان زاهية وحركات مرحة تجعل كل تفاعل تجربة ممتعة ومبهجة</p>
            </div>
            <div class="feature-card artistic-glass">
                <div class="feature-icon">🌈</div>
                <h3>ألوان متدفقة</h3>
                <p>تدرجات لونية متحركة تتغير باستمرار لتعكس حالة الطاقة وتضفي جواً من الحيوية والنشاط على التطبيق</p>
            </div>
            <div class="feature-card artistic-glass">
                <div class="feature-icon">✨</div>
                <h3>تأثيرات سحرية</h3>
                <p>حركات وتأثيرات بصرية ساحرة تحول إدارة الطاقة إلى تجربة تفاعلية ممتعة ومليئة بالمفاجآت الجميلة</p>
            </div>
            <div class="feature-card artistic-glass">
                <div class="feature-icon">🎭</div>
                <h3>شخصية مرحة</h3>
                <p>واجهة ذات شخصية مرحة ومبهجة تجعل التعامل مع التكنولوجيا المعقدة أمراً ممتعاً وسهلاً للجميع</p>
            </div>
            <div class="feature-card artistic-glass">
                <div class="feature-icon">🎪</div>
                <h3>تجربة مسرحية</h3>
                <p>كل عنصر في التطبيق يحكي قصة ويقدم عرضاً بصرياً مذهلاً يحول استخدام التطبيق إلى مغامرة شيقة</p>
            </div>
            <div class="feature-card artistic-glass">
                <div class="feature-icon">🎵</div>
                <h3>إيقاع متناغم</h3>
                <p>حركات متناغمة وإيقاعات بصرية تخلق تجربة متكاملة تجمع بين الوظيفة والجمال في تناغم مثالي</p>
            </div>
        </div>
    </section>

    <script>
        // Creative Hover Effects
        document.querySelectorAll('.feature-card').forEach((card, index) => {
            card.addEventListener('mouseenter', function() {
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                this.style.background = `linear-gradient(135deg, rgba(255, 255, 255, 0.4), ${randomColor}20)`;
                this.style.borderColor = randomColor + '60';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.background = '';
                this.style.borderColor = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Creative Navbar Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))';
                navbar.style.backdropFilter = 'blur(25px)';
                navbar.style.transform = 'translateX(-50%) scale(0.95)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))';
                navbar.style.backdropFilter = 'blur(20px)';
                navbar.style.transform = 'translateX(-50%) scale(1)';
            }
        });

        // Random Color Changes
        setInterval(() => {
            const shapes = document.querySelectorAll('.creative-shape');
            shapes.forEach(shape => {
                const hue = Math.random() * 360;
                shape.style.filter = `hue-rotate(${hue}deg)`;
            });
        }, 3000);

        // Creative Click Effects
        document.addEventListener('click', function(e) {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            
            const ripple = document.createElement('div');
            ripple.style.position = 'fixed';
            ripple.style.left = e.clientX + 'px';
            ripple.style.top = e.clientY + 'px';
            ripple.style.width = '20px';
            ripple.style.height = '20px';
            ripple.style.background = randomColor;
            ripple.style.borderRadius = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.pointerEvents = 'none';
            ripple.style.zIndex = '9999';
            ripple.style.animation = 'rippleEffect 0.6s ease-out forwards';
            
            document.body.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes rippleEffect {
                0% {
                    transform: translate(-50%, -50%) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translate(-50%, -50%) scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        console.log('🎨✨ Creative Artistic Demo Loaded Successfully! ✨🎨');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - UI Design Previews</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 30px;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .preview-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .preview-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: #ff7200;
        }

        .preview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 114, 0, 0.1), transparent);
            transition: left 0.5s;
        }

        .preview-card:hover::before {
            left: 100%;
        }

        .preview-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .preview-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .glassmorphism .preview-icon {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            backdrop-filter: blur(10px);
        }

        .cyberpunk .preview-icon {
            background: linear-gradient(135deg, #00ff88, #00ccff);
            color: #000;
        }

        .minimalist .preview-icon {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #333;
        }

        .neumorphism .preview-icon {
            background: #2a2a2a;
            box-shadow: 8px 8px 16px #1a1a1a, -8px -8px 16px #3a3a3a;
        }

        .gradient .preview-icon {
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
        }

        .preview-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .preview-subtitle {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .preview-description {
            margin-bottom: 20px;
            line-height: 1.6;
            opacity: 0.9;
        }

        .preview-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .preview-features li {
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }

        .preview-features li::before {
            content: '✨';
            position: absolute;
            left: 0;
        }

        .preview-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: #ff7200;
            border: 1px solid #ff7200;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .comparison-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            margin-top: 50px;
        }

        .comparison-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 30px;
            color: #ff7200;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .comparison-table th {
            background: rgba(255, 114, 0, 0.2);
            font-weight: 600;
        }

        .rating {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #ffd700;
        }

        .footer-note {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border-left: 4px solid #ff7200;
        }

        @media (max-width: 768px) {
            .preview-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .comparison-table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Energy.AI UI Design Previews</h1>
            <p>اختر التصميم المفضل لديك من النماذج الحديثة والرائعة التالية</p>
            <div style="background: rgba(255, 114, 0, 0.1); padding: 15px; border-radius: 10px; margin-top: 20px;">
                <strong>💡 نصيحة:</strong> انقر على "معاينة مباشرة" لرؤية التصميم كاملاً، أو "عرض الكود" لرؤية التفاصيل التقنية
            </div>
        </div>

        <div class="preview-grid">
            <!-- Modern Glassmorphism -->
            <div class="preview-card glassmorphism">
                <div class="preview-header">
                    <div class="preview-icon">🔮</div>
                    <div>
                        <div class="preview-title">Modern Glassmorphism</div>
                        <div class="preview-subtitle">التصميم الزجاجي الحديث</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم عصري يستخدم تأثيرات الزجاج الضبابي والشفافية لإنشاء واجهة أنيقة ومتطورة. يتميز بالعمق البصري والحداثة.
                </div>
                <ul class="preview-features">
                    <li>تأثيرات زجاجية متقدمة</li>
                    <li>شفافية وضبابية ديناميكية</li>
                    <li>ألوان متدرجة ناعمة</li>
                    <li>تفاعلات سلسة</li>
                    <li>تصميم متجاوب كامل</li>
                </ul>
                <div class="preview-actions">
                    <a href="glassmorphism-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="glassmorphism-enhanced-demo.html" class="btn btn-primary" style="background: linear-gradient(135deg, #4caf50, #8bc34a);">
                        ✨ النسخة المحسنة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- Cyberpunk Energy -->
            <div class="preview-card cyberpunk">
                <div class="preview-header">
                    <div class="preview-icon">⚡</div>
                    <div>
                        <div class="preview-title">Cyberpunk Energy</div>
                        <div class="preview-subtitle">تصميم مستقبلي نيون</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم مستقبلي جريء يستخدم ألوان النيون الزاهية والتأثيرات الضوئية لإنشاء تجربة بصرية مثيرة ومليئة بالطاقة.
                </div>
                <ul class="preview-features">
                    <li>ألوان نيون متوهجة</li>
                    <li>تأثيرات ضوئية متحركة</li>
                    <li>خطوط مستقبلية</li>
                    <li>حركات ديناميكية</li>
                    <li>تصميم عالي التباين</li>
                </ul>
                <div class="preview-actions">
                    <a href="cyberpunk-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- Minimalist Clean -->
            <div class="preview-card minimalist">
                <div class="preview-header">
                    <div class="preview-icon">🎯</div>
                    <div>
                        <div class="preview-title">Minimalist Clean</div>
                        <div class="preview-subtitle">تصميم بسيط وأنيق</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم بسيط وواضح يركز على المحتوى والوظائف الأساسية. يتميز بالوضوح والسهولة في الاستخدام مع لمسة عصرية.
                </div>
                <ul class="preview-features">
                    <li>تصميم نظيف ومرتب</li>
                    <li>مساحات بيضاء متوازنة</li>
                    <li>ألوان هادئة ومريحة</li>
                    <li>تركيز على المحتوى</li>
                    <li>سرعة تحميل عالية</li>
                </ul>
                <div class="preview-actions">
                    <a href="minimalist-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- Dark Neumorphism -->
            <div class="preview-card neumorphism">
                <div class="preview-header">
                    <div class="preview-icon">🌙</div>
                    <div>
                        <div class="preview-title">Dark Neumorphism</div>
                        <div class="preview-subtitle">تصميم ثلاثي الأبعاد الناعم</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم ثلاثي الأبعاد ناعم يستخدم الظلال والإضاءة لإنشاء عناصر تبدو وكأنها منحوتة من المادة نفسها.
                </div>
                <ul class="preview-features">
                    <li>تأثيرات ثلاثية الأبعاد</li>
                    <li>ظلال ناعمة ومتدرجة</li>
                    <li>ملمس طبيعي</li>
                    <li>تفاعلات لمسية</li>
                    <li>تصميم مريح للعين</li>
                </ul>
                <div class="preview-actions">
                    <a href="neumorphism-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- Gradient Futuristic -->
            <div class="preview-card gradient">
                <div class="preview-header">
                    <div class="preview-icon">🌈</div>
                    <div>
                        <div class="preview-title">Gradient Futuristic</div>
                        <div class="preview-subtitle">تصميم مستقبلي بتدرجات</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم مستقبلي يستخدم التدرجات الملونة الجريئة والحركات السلسة لإنشاء تجربة بصرية ديناميكية ومثيرة.
                </div>
                <ul class="preview-features">
                    <li>تدرجات ملونة متحركة</li>
                    <li>حركات سلسة ومتدفقة</li>
                    <li>تأثيرات بصرية مذهلة</li>
                    <li>ألوان زاهية ومتناسقة</li>
                    <li>تصميم تفاعلي متقدم</li>
                </ul>
                <div class="preview-actions">
                    <a href="gradient-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- Bonus: Hybrid Design -->
            <div class="preview-card" style="border: 2px solid #ff7200; background: linear-gradient(135deg, rgba(255, 114, 0, 0.1), rgba(25, 118, 210, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #ff7200, #1976d2);">🚀</div>
                    <div>
                        <div class="preview-title">Hybrid Energy Design</div>
                        <div class="preview-subtitle">تصميم مختلط مخصص</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم مخصص يجمع أفضل عناصر من جميع التصاميم السابقة، مصمم خصيصاً لموقع Energy.AI ليعكس هوية العلامة التجارية.
                </div>
                <ul class="preview-features">
                    <li>مزيج من أفضل التقنيات</li>
                    <li>مخصص لهوية Energy.AI</li>
                    <li>تحسين للأداء والسرعة</li>
                    <li>تجربة مستخدم متميزة</li>
                    <li>قابلية تخصيص عالية</li>
                </ul>
                <div class="preview-actions">
                    <a href="hybrid-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <!-- New Ultra Beautiful Designs -->
            <div class="preview-card" style="border: 2px solid #ffd700; background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(184, 134, 11, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #ffd700, #b8860b);">👑</div>
                    <div>
                        <div class="preview-title">Luxury Premium</div>
                        <div class="preview-subtitle">تصميم فاخر ذهبي</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم فاخر بألوان ذهبية معدنية وتأثيرات ثلاثية الأبعاد متقدمة مع حركات سينمائية مذهلة وخطوط فاخرة أنيقة.
                </div>
                <ul class="preview-features">
                    <li>ألوان ذهبية ومعدنية فاخرة</li>
                    <li>تأثيرات ثلاثية الأبعاد متقدمة</li>
                    <li>حركات سينمائية مذهلة</li>
                    <li>خطوط فاخرة وأنيقة</li>
                    <li>تجربة VIP حصرية</li>
                </ul>
                <div class="preview-actions">
                    <a href="luxury-premium-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <div class="preview-card" style="border: 2px solid #7877c6; background: linear-gradient(135deg, rgba(120, 119, 198, 0.1), rgba(255, 119, 198, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #7877c6, #ff77c6);">🌌</div>
                    <div>
                        <div class="preview-title">Cosmic Energy</div>
                        <div class="preview-subtitle">تصميم كوني ساحر</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم كوني ساحر مع خلفية فضائية متحركة ونجوم متلألئة وألوان كونية ساحرة وحركات مجرية مذهلة.
                </div>
                <ul class="preview-features">
                    <li>خلفية فضائية متحركة مع نجوم</li>
                    <li>تأثيرات جسيمات متقدمة</li>
                    <li>ألوان كونية ساحرة</li>
                    <li>حركات مجرية مذهلة</li>
                    <li>تجربة فضائية غامرة</li>
                </ul>
                <div class="preview-actions">
                    <a href="cosmic-energy-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <div class="preview-card" style="border: 2px solid #4a90e2; background: linear-gradient(135deg, rgba(74, 144, 226, 0.1), rgba(155, 89, 182, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #4a90e2, #9b59b6);">💎</div>
                    <div>
                        <div class="preview-title">Crystal Glass Pro</div>
                        <div class="preview-subtitle">تصميم كريستالي نقي</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم كريستالي فائق الجودة مع انكسارات ضوئية واقعية وشفافية متدرجة ديناميكية وتأثيرات قوس قزح ساحرة.
                </div>
                <ul class="preview-features">
                    <li>تأثيرات زجاجية فائقة الجودة</li>
                    <li>انكسارات ضوئية واقعية</li>
                    <li>شفافية متدرجة ديناميكية</li>
                    <li>تأثيرات قوس قزح</li>
                    <li>نقاء كريستالي مذهل</li>
                </ul>
                <div class="preview-actions">
                    <a href="crystal-glass-pro-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <div class="preview-card" style="border: 2px solid #00ffff; background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #00ffff, #ff00ff);">🚀</div>
                    <div>
                        <div class="preview-title">Ultra Modern</div>
                        <div class="preview-subtitle">تصميم مستقبلي فائق</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم مستقبلي فائق التطور مع شبكة هندسية متحركة وألوان نيون متقدمة وتقنيات مستقبلية مذهلة.
                </div>
                <ul class="preview-features">
                    <li>تصميم مستقبلي فائق</li>
                    <li>شبكة هندسية متحركة</li>
                    <li>ألوان نيون متقدمة</li>
                    <li>تقنيات مستقبلية</li>
                    <li>واجهة عصرية متطورة</li>
                </ul>
                <div class="preview-actions">
                    <a href="ultra-modern-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <div class="preview-card" style="border: 2px solid #d2b48c; background: linear-gradient(135deg, rgba(210, 180, 140, 0.1), rgba(222, 184, 135, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #d2b48c, #deb887);">🌸</div>
                    <div>
                        <div class="preview-title">Elegant Soft</div>
                        <div class="preview-subtitle">تصميم ناعم أنيق</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم ناعم وأنيق بألوان هادئة مريحة للعين ولمسات طبيعية جميلة مع تجربة مريحة ومتناغمة.
                </div>
                <ul class="preview-features">
                    <li>تصميم ناعم وأنيق</li>
                    <li>ألوان هادئة مريحة</li>
                    <li>لمسات طبيعية جميلة</li>
                    <li>تجربة مريحة ومتناغمة</li>
                    <li>جمال وظيفي متوازن</li>
                </ul>
                <div class="preview-actions">
                    <a href="elegant-soft-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>

            <div class="preview-card" style="border: 2px solid #ff6b6b; background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));">
                <div class="preview-header">
                    <div class="preview-icon" style="background: linear-gradient(135deg, #ff6b6b, #4ecdc4);">🎨</div>
                    <div>
                        <div class="preview-title">Creative Artistic</div>
                        <div class="preview-subtitle">تصميم فني إبداعي</div>
                    </div>
                </div>
                <div class="preview-description">
                    تصميم فني إبداعي مع ألوان متدفقة متحركة وحركات مرحة وتأثيرات بصرية ساحرة تحول التطبيق إلى تحفة فنية.
                </div>
                <ul class="preview-features">
                    <li>تصميم فني إبداعي</li>
                    <li>ألوان متدفقة متحركة</li>
                    <li>حركات مرحة وممتعة</li>
                    <li>تأثيرات بصرية ساحرة</li>
                    <li>تجربة فنية تفاعلية</li>
                </ul>
                <div class="preview-actions">
                    <a href="creative-artistic-demo.html" class="btn btn-primary">
                        👁️ معاينة مباشرة
                    </a>
                    <a href="#" class="btn btn-secondary">
                        📝 عرض الكود
                    </a>
                </div>
            </div>
        </div>

        <div class="comparison-section">
            <h2 class="comparison-title">📊 مقارنة التصاميم</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>التصميم</th>
                        <th>الحداثة</th>
                        <th>سهولة الاستخدام</th>
                        <th>الأداء</th>
                        <th>التميز البصري</th>
                        <th>التوافق</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Glassmorphism</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr>
                        <td><strong>Cyberpunk</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                    </tr>
                    <tr>
                        <td><strong>Minimalist</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr>
                        <td><strong>Neumorphism</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr>
                        <td><strong>Gradient</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(255, 114, 0, 0.1);">
                        <td><strong>Hybrid Energy</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(255, 215, 0, 0.1);">
                        <td><strong>Luxury Premium</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(120, 119, 198, 0.1);">
                        <td><strong>Cosmic Energy</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(74, 144, 226, 0.1);">
                        <td><strong>Crystal Glass Pro</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(0, 255, 255, 0.1);">
                        <td><strong>Ultra Modern</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(210, 180, 140, 0.1);">
                        <td><strong>Elegant Soft</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                    </tr>
                    <tr style="background: rgba(255, 107, 107, 0.1);">
                        <td><strong>Creative Artistic</strong></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐⭐</div></td>
                        <td><div class="rating">⭐⭐⭐⭐</div></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="footer-note">
            <h3>🎨 مجموعة شاملة من التصاميم الفائقة</h3>
            <p>تم إنشاء <strong>12 تصميم UI متكامل</strong> عالي الجودة لموقع Energy.AI، كل تصميم يقدم تجربة بصرية فريدة ومتميزة:</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>6 تصاميم أساسية:</strong> Glassmorphism, Cyberpunk, Minimalist, Neumorphism, Gradient, Hybrid</li>
                <li><strong>6 تصاميم فائقة الجمال:</strong> Luxury Premium, Cosmic Energy, Crystal Glass Pro, Ultra Modern, Elegant Soft, Creative Artistic</li>
            </ul>
            <p><strong>جميع التصاميم:</strong> قابلة للتخصيص، متجاوبة 100%، محسنة للأداء، وتحتوي على تأثيرات تفاعلية متقدمة.</p>
            <p style="margin-top: 15px;"><strong>🚀 الخطوة التالية:</strong> اختر التصميم المفضل لديك وسنبدأ في تطبيقه على موقع Energy.AI بالبنية المنظمة الجديدة!</p>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.querySelectorAll('.preview-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click tracking
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.textContent.includes('عرض الكود')) {
                    e.preventDefault();
                    alert('سيتم إنشاء الكود بعد اختيار التصميم المفضل! 🚀');
                }
            });
        });

        console.log('🎨 UI Preview Page Loaded Successfully!');
    </script>
</body>
</html>

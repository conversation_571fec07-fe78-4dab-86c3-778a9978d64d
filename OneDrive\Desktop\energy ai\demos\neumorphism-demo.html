<!DOCTYPE html>
<html lang="en" data-theme="neumorphism">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Dark Neumorphism Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #2a2a2a;
            color: #e0e0e0;
            overflow-x: hidden;
            min-height: 100vh;
        }

        /* Neumorphism Base */
        .neu-inset {
            background: #2a2a2a;
            box-shadow: inset 8px 8px 16px #1a1a1a, inset -8px -8px 16px #3a3a3a;
        }

        .neu-outset {
            background: #2a2a2a;
            box-shadow: 8px 8px 16px #1a1a1a, -8px -8px 16px #3a3a3a;
        }

        .neu-flat {
            background: #2a2a2a;
            box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
        }

        .neu-pressed {
            background: #2a2a2a;
            box-shadow: inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff7200;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #ff7200;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        .nav-menu a {
            color: #e0e0e0;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .nav-menu a:hover {
            color: #ff7200;
            background: #2a2a2a;
            box-shadow: inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            font-family: inherit;
        }

        .btn-primary {
            background: #ff7200;
            color: white;
            box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
        }

        .btn-primary:hover {
            box-shadow: 2px 2px 4px #1a1a1a, -2px -2px 4px #3a3a3a;
            transform: translateY(1px);
        }

        .btn-primary:active {
            box-shadow: inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a;
        }

        .btn-neu {
            background: #2a2a2a;
            color: #e0e0e0;
            box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
        }

        .btn-neu:hover {
            color: #ff7200;
            box-shadow: 2px 2px 4px #1a1a1a, -2px -2px 4px #3a3a3a;
        }

        .btn-neu:active {
            box-shadow: inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
        }

        .hero-content {
            max-width: 800px;
            padding: 60px 40px;
            border-radius: 30px;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: 20px;
            color: #e0e0e0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .hero .highlight {
            color: #ff7200;
            text-shadow: 0 0 20px rgba(255, 114, 0, 0.5);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            color: #b0b0b0;
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            color: #e0e0e0;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            border-radius: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 12px 12px 24px #1a1a1a, -12px -12px 24px #3a3a3a;
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #ff7200;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            box-shadow: inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #e0e0e0;
        }

        .feature-card p {
            color: #b0b0b0;
            line-height: 1.6;
        }

        /* Interactive Elements */
        .interactive-section {
            padding: 100px 20px;
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }

        .control-panel {
            padding: 40px;
            border-radius: 30px;
            margin-top: 40px;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .control-item {
            text-align: center;
        }

        .control-button {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            border: none;
            background: #2a2a2a;
            color: #ff7200;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }

        .control-button:hover {
            color: #ffb74d;
            box-shadow: 2px 2px 4px #1a1a1a, -2px -2px 4px #3a3a3a;
        }

        .control-button:active {
            box-shadow: inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a;
        }

        .control-button.active {
            color: #4caf50;
            box-shadow: inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a;
        }

        .control-label {
            font-weight: 500;
            color: #e0e0e0;
        }

        /* Slider */
        .slider-container {
            margin: 30px 0;
        }

        .slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #2a2a2a;
            box-shadow: inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #ff7200;
            box-shadow: 2px 2px 4px #1a1a1a, -2px -2px 4px #3a3a3a;
            cursor: pointer;
        }

        .slider::-webkit-slider-thumb:hover {
            box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
        }

        /* Contact Section */
        .contact {
            padding: 100px 20px;
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .contact-form {
            padding: 40px;
            border-radius: 30px;
            margin-top: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #e0e0e0;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 15px 20px;
            background: #2a2a2a;
            border: none;
            border-radius: 15px;
            color: #e0e0e0;
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.3s ease;
            box-shadow: inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            box-shadow: inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: #808080;
        }

        /* Footer */
        .footer {
            padding: 40px 20px;
            text-align: center;
            margin-top: 50px;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px;
            border-radius: 25px;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: #2a2a2a;
            border: none;
            border-radius: 15px;
            color: #e0e0e0;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
        }

        .back-btn:hover {
            color: #ff7200;
            box-shadow: 2px 2px 4px #1a1a1a, -2px -2px 4px #3a3a3a;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .controls-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Animations */
        @keyframes neuPulse {
            0%, 100% {
                box-shadow: 4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a;
            }
            50% {
                box-shadow: 8px 8px 16px #1a1a1a, -8px -8px 16px #3a3a3a;
            }
        }

        .pulse {
            animation: neuPulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← Back</a>

    <!-- Navigation -->
    <nav class="navbar neu-outset">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon neu-flat">⚡</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#features">Features</a></li>
                <li><a href="#interactive">Interactive</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="#contact" class="btn btn-primary">Get Started</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content neu-outset">
            <h1>Smart Energy with <span class="highlight">Neumorphic</span> Design</h1>
            <p>Experience the future of energy management through our innovative neumorphic interface that combines functionality with beautiful, tactile design.</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-primary">Explore Features</a>
                <a href="#interactive" class="btn btn-neu">Try Interactive Demo</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">Tactile Energy Solutions</h2>
        <div class="features-grid">
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">🧠</div>
                <h3>Intelligent Control</h3>
                <p>Intuitive neumorphic controls that respond to your touch with realistic feedback, making energy management feel natural and engaging.</p>
            </div>
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">📊</div>
                <h3>Visual Analytics</h3>
                <p>Beautiful data visualization with depth and dimension, presenting complex energy data in an easily digestible neumorphic interface.</p>
            </div>
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">🎛️</div>
                <h3>Tactile Interface</h3>
                <p>Physical-feeling digital controls that provide satisfying feedback, making every interaction with your energy system feel real and responsive.</p>
            </div>
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">🌙</div>
                <h3>Dark Mode Optimized</h3>
                <p>Designed specifically for dark environments with soft shadows and highlights that reduce eye strain during extended use.</p>
            </div>
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">🔧</div>
                <h3>Customizable Controls</h3>
                <p>Personalize your interface with adjustable neumorphic elements that adapt to your preferences and usage patterns.</p>
            </div>
            <div class="feature-card neu-outset">
                <div class="feature-icon neu-flat">📱</div>
                <h3>Mobile Optimized</h3>
                <p>Neumorphic design that works beautifully across all devices, maintaining the tactile feel even on mobile screens.</p>
            </div>
        </div>
    </section>

    <!-- Interactive Section -->
    <section class="interactive-section" id="interactive">
        <h2 class="section-title">Interactive Demo</h2>
        <p style="color: #b0b0b0; margin-bottom: 20px;">Try our neumorphic controls and feel the difference</p>
        
        <div class="control-panel neu-inset">
            <h3 style="color: #e0e0e0; margin-bottom: 30px;">Energy Control Panel</h3>
            
            <div class="controls-grid">
                <div class="control-item">
                    <button class="control-button neu-outset" onclick="toggleControl(this, '💡')">💡</button>
                    <div class="control-label">Lighting</div>
                </div>
                <div class="control-item">
                    <button class="control-button neu-outset" onclick="toggleControl(this, '❄️')">❄️</button>
                    <div class="control-label">Cooling</div>
                </div>
                <div class="control-item">
                    <button class="control-button neu-outset" onclick="toggleControl(this, '🔥')">🔥</button>
                    <div class="control-label">Heating</div>
                </div>
                <div class="control-item">
                    <button class="control-button neu-outset" onclick="toggleControl(this, '🔌')">🔌</button>
                    <div class="control-label">Power</div>
                </div>
            </div>
            
            <div class="slider-container">
                <label style="color: #e0e0e0; margin-bottom: 10px; display: block;">Energy Efficiency: <span id="efficiency-value">75%</span></label>
                <input type="range" class="slider" min="0" max="100" value="75" id="efficiency-slider">
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <h2 class="section-title">Get in Touch</h2>
        <p style="color: #b0b0b0;">Experience the neumorphic difference in energy management</p>
        
        <form class="contact-form neu-inset">
            <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" placeholder="Enter your full name" required>
            </div>
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" placeholder="+****************">
            </div>
            <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" rows="5" placeholder="Tell us about your energy needs..." required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">
                Send Message
            </button>
        </form>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content neu-outset">
            <p style="color: #e0e0e0;">&copy; 2024 Energy.AI - All rights reserved</p>
            <p style="margin-top: 10px; color: #b0b0b0;">Dark Neumorphism Design - Demo Version</p>
        </div>
    </footer>

    <script>
        // Interactive Controls
        function toggleControl(button, icon) {
            button.classList.toggle('active');
            if (button.classList.contains('active')) {
                button.style.boxShadow = 'inset 4px 4px 8px #1a1a1a, inset -4px -4px 8px #3a3a3a';
                button.style.color = '#4caf50';
            } else {
                button.style.boxShadow = '4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a';
                button.style.color = '#ff7200';
            }
        }

        // Efficiency Slider
        const slider = document.getElementById('efficiency-slider');
        const valueDisplay = document.getElementById('efficiency-value');

        slider.addEventListener('input', function() {
            valueDisplay.textContent = this.value + '%';
        });

        // Form Submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'Sending...';
            submitBtn.style.boxShadow = 'inset 2px 2px 4px #1a1a1a, inset -2px -2px 4px #3a3a3a';
            
            setTimeout(() => {
                submitBtn.textContent = 'Message Sent!';
                submitBtn.style.background = '#4caf50';
                
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.style.background = '#ff7200';
                    submitBtn.style.boxShadow = '4px 4px 8px #1a1a1a, -4px -4px 8px #3a3a3a';
                    this.reset();
                }, 2000);
            }, 1500);
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add pulse effect to random elements
        setInterval(() => {
            const cards = document.querySelectorAll('.feature-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.classList.add('pulse');
            setTimeout(() => {
                randomCard.classList.remove('pulse');
            }, 2000);
        }, 5000);

        console.log('🌙 Dark Neumorphism Demo Loaded Successfully!');
    </script>
</body>
</html>

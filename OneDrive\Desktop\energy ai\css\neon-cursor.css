/* Reset and base styles */
body {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

/* Neon cursor container */
#neon-cursor-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -10; /* أسفل كل شيء */
    pointer-events: none;
    overflow: visible;
}

#neon-cursor-container canvas {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    mix-blend-mode: screen;
    pointer-events: none;
    opacity: 0.9;
    filter: blur(8px); /* إضافة تأثير توهج خفيف */
}

/* Layer management */
.main {
    position: relative;
    z-index: 1;
}

.geometric-background {
    position: fixed;
    z-index: -15; /* Much lower z-index to stay behind everything */
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background: linear-gradient(135deg,
        rgba(0,0,0,0.1) 0%, /* Reduced opacity */
        rgba(30,30,60,0.08) 100%); /* Reduced opacity */
}

/* Optimize performance */
#neon-cursor-container canvas {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #neon-cursor-container canvas {
        opacity: 0.6;
    }
}

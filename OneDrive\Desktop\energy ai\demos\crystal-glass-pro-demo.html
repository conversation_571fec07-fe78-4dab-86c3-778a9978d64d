<!DOCTYPE html>
<html lang="en" data-theme="crystal">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Crystal Glass Pro Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
            color: #2c3e50;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Crystal Background */
        .crystal-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(155, 89, 182, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(52, 152, 219, 0.06) 0%, transparent 50%),
                linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
        }

        /* Crystal Refractions */
        .crystal-refractions {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .refraction {
            position: absolute;
            background: linear-gradient(45deg, 
                rgba(255, 0, 150, 0.1), 
                rgba(0, 255, 255, 0.1), 
                rgba(255, 255, 0, 0.1), 
                rgba(150, 0, 255, 0.1));
            border-radius: 50%;
            animation: crystalRefract 8s ease-in-out infinite;
            filter: blur(1px);
        }

        .refraction:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 20%;
            left: 15%;
            animation-delay: 0s;
        }

        .refraction:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }

        .refraction:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 25%;
            left: 25%;
            animation-delay: 4s;
        }

        .refraction:nth-child(4) {
            width: 120px;
            height: 120px;
            top: 35%;
            right: 35%;
            animation-delay: 6s;
        }

        @keyframes crystalRefract {
            0%, 100% { 
                transform: rotate(0deg) scale(1);
                opacity: 0.3;
                filter: blur(1px) hue-rotate(0deg);
            }
            25% { 
                transform: rotate(90deg) scale(1.2);
                opacity: 0.6;
                filter: blur(2px) hue-rotate(90deg);
            }
            50% { 
                transform: rotate(180deg) scale(0.8);
                opacity: 0.4;
                filter: blur(3px) hue-rotate(180deg);
            }
            75% { 
                transform: rotate(270deg) scale(1.1);
                opacity: 0.7;
                filter: blur(1.5px) hue-rotate(270deg);
            }
        }

        /* Crystal Glass */
        .crystal-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.25) 0%, 
                rgba(255, 255, 255, 0.1) 50%, 
                rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 2px 0 rgba(255, 255, 255, 0.4),
                inset 0 -2px 0 rgba(255, 255, 255, 0.1),
                0 0 20px rgba(74, 144, 226, 0.1);
            position: relative;
            overflow: hidden;
        }

        .crystal-glass::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.4) 0%, 
                transparent 30%, 
                transparent 70%, 
                rgba(255, 255, 255, 0.2) 100%);
            pointer-events: none;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #4a90e2, #9b59b6, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
            filter: drop-shadow(0 2px 4px rgba(74, 144, 226, 0.3));
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #4a90e2, #9b59b6);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 
                0 4px 20px rgba(74, 144, 226, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, 
                rgba(255, 255, 255, 0.4), 
                transparent, 
                rgba(255, 255, 255, 0.4));
            animation: crystalSpin 4s linear infinite;
        }

        @keyframes crystalSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        .nav-menu a:hover {
            color: #4a90e2;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
        }

        /* Crystal Buttons */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            position: relative;
            overflow: hidden;
        }

        .btn-crystal {
            background: linear-gradient(135deg, #4a90e2, #9b59b6);
            color: white;
            box-shadow: 
                0 4px 20px rgba(74, 144, 226, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-crystal:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 8px 30px rgba(74, 144, 226, 0.4),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
        }

        .btn-crystal::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .btn-crystal:hover::before {
            left: 100%;
        }

        .btn-glass-crystal {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            color: #4a90e2;
            border: 2px solid rgba(74, 144, 226, 0.3);
        }

        .btn-glass-crystal:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.2);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 0 20px;
            position: relative;
        }

        .hero-content {
            max-width: 900px;
            z-index: 2;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 4.5rem);
            font-weight: 900;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #4a90e2, #9b59b6, #3498db, #e74c3c, #f39c12);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: crystalGradient 8s ease-in-out infinite;
            line-height: 1.2;
            filter: drop-shadow(0 4px 8px rgba(74, 144, 226, 0.3));
        }

        @keyframes crystalGradient {
            0%, 100% { background-position: 0% 50%; }
            20% { background-position: 100% 0%; }
            40% { background-position: 100% 100%; }
            60% { background-position: 0% 100%; }
            80% { background-position: 0% 0%; }
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 40px;
            color: #34495e;
            line-height: 1.6;
            font-weight: 400;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Crystal Prisms */
        .crystal-prism {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            animation: crystalFloat 12s ease-in-out infinite;
        }

        .crystal-prism:nth-child(1) {
            border-left: 50px solid transparent;
            border-right: 50px solid transparent;
            border-bottom: 100px solid rgba(74, 144, 226, 0.1);
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .crystal-prism:nth-child(2) {
            border-left: 40px solid transparent;
            border-right: 40px solid transparent;
            border-bottom: 80px solid rgba(155, 89, 182, 0.1);
            top: 70%;
            right: 15%;
            animation-delay: 4s;
        }

        .crystal-prism:nth-child(3) {
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
            border-bottom: 60px solid rgba(52, 152, 219, 0.1);
            bottom: 20%;
            left: 20%;
            animation-delay: 8s;
        }

        @keyframes crystalFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            33% { 
                transform: translateY(-30px) rotate(120deg);
                opacity: 0.7;
            }
            66% { 
                transform: translateY(30px) rotate(240deg);
                opacity: 0.5;
            }
        }

        /* Features Section */
        .features {
            padding: 100px 20px;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #4a90e2, #9b59b6, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(74, 144, 226, 0.3));
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
            overflow: hidden;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(74, 144, 226, 0.05), 
                rgba(155, 89, 182, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover::after {
            opacity: 1;
        }

        .feature-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.1),
                0 0 30px rgba(74, 144, 226, 0.2);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #4a90e2, #9b59b6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            box-shadow: 
                0 8px 30px rgba(74, 144, 226, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: -100%;
            left: -100%;
            width: 300%;
            height: 300%;
            background: conic-gradient(from 0deg, 
                transparent, 
                rgba(255, 255, 255, 0.4), 
                transparent, 
                rgba(255, 255, 255, 0.2), 
                transparent);
            animation: crystalIconSpin 6s linear infinite;
        }

        @keyframes crystalIconSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-card h3 {
            font-size: 1.6rem;
            margin-bottom: 15px;
            color: #4a90e2;
            font-weight: 700;
        }

        .feature-card p {
            color: #34495e;
            line-height: 1.6;
            font-weight: 400;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(74, 144, 226, 0.3);
            border-radius: 15px;
            color: #4a90e2;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-5px);
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.2);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .section-title {
                font-size: 2.2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Crystal Background -->
    <div class="crystal-bg"></div>
    
    <!-- Crystal Refractions -->
    <div class="crystal-refractions">
        <div class="refraction"></div>
        <div class="refraction"></div>
        <div class="refraction"></div>
        <div class="refraction"></div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة للمعرض</a>

    <!-- Navigation -->
    <nav class="navbar crystal-glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">💎</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الكريستال</a></li>
                <li><a href="#crystal">الانكسار</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <a href="#contact" class="btn btn-crystal">ابدأ التجربة الكريستالية</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="crystal-prism"></div>
        <div class="crystal-prism"></div>
        <div class="crystal-prism"></div>
        
        <div class="hero-content">
            <h1>الطاقة الكريستالية النقية</h1>
            <p>اكتشف عالم الطاقة النقية مع تقنيات الكريستال المتقدمة، حيث تنكسر أشعة الضوء لتكشف عن إمكانيات لامحدودة في عالم الطاقة الذكية</p>
            <div class="hero-actions">
                <a href="#features" class="btn btn-crystal">استكشف الكريستال</a>
                <a href="#contact" class="btn btn-glass-crystal">انضم للتجربة</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">قوى الكريستال المتقدمة</h2>
        <div class="features-grid">
            <div class="feature-card crystal-glass">
                <div class="feature-icon">💎</div>
                <h3>نقاء كريستالي</h3>
                <p>تقنيات طاقة نقية كالكريستال مع شفافية مطلقة في البيانات وانكسارات ضوئية تكشف كل التفاصيل</p>
            </div>
            <div class="feature-card crystal-glass">
                <div class="feature-icon">🌈</div>
                <h3>انكسار الطيف</h3>
                <p>تحليل متطور للطاقة عبر انكسار البيانات إلى طيف كامل من المعلومات الدقيقة والمفصلة</p>
            </div>
            <div class="feature-card crystal-glass">
                <div class="feature-icon">✨</div>
                <h3>بريق متلألئ</h3>
                <p>واجهات متلألئة تعكس جمال الكريستال مع تفاعلات بصرية مذهلة تجعل كل لحظة استخدام تجربة ساحرة</p>
            </div>
            <div class="feature-card crystal-glass">
                <div class="feature-icon">🔮</div>
                <h3>رؤية كريستالية</h3>
                <p>رؤية واضحة ونقية لمستقبل الطاقة مع تنبؤات دقيقة كوضوح الكريستال لاستهلاك الطاقة</p>
            </div>
            <div class="feature-card crystal-glass">
                <div class="feature-icon">💠</div>
                <h3>هندسة مثالية</h3>
                <p>تصميم هندسي مثالي مستوحى من البنية الكريستالية لتحقيق أقصى كفاءة وجمال في الاستخدام</p>
            </div>
            <div class="feature-card crystal-glass">
                <div class="feature-icon">🌟</div>
                <h3>إشعاع طاقي</h3>
                <p>إشعاع طاقة إيجابية من خلال تقنيات متقدمة تحاكي قوة الكريستال في تضخيم وتنقية الطاقة</p>
            </div>
        </div>
    </section>

    <script>
        // Crystal Hover Effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.15), 0 0 40px rgba(74, 144, 226, 0.3)';
                this.style.filter = 'brightness(1.1)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
                this.style.filter = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced Navbar Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1))';
                navbar.style.backdropFilter = 'blur(35px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))';
                navbar.style.backdropFilter = 'blur(30px)';
            }
        });

        // Crystal Light Effects
        setInterval(() => {
            const cards = document.querySelectorAll('.feature-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            randomCard.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.1), 0 0 30px rgba(74, 144, 226, 0.4)';
            randomCard.style.filter = 'brightness(1.2) saturate(1.2)';
            setTimeout(() => {
                randomCard.style.boxShadow = '';
                randomCard.style.filter = '';
            }, 2000);
        }, 3000);

        console.log('💎✨ Crystal Glass Pro Demo Loaded Successfully! ✨💎');
    </script>
</body>
</html>

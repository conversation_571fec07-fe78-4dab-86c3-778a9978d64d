<!DOCTYPE html>
<html lang="en" data-theme="elegant-soft">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Elegant Soft Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Crimson+Text:wght@400;600&family=Inter:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #faf7f2 0%, #f5f1eb 25%, #f0ebe4 50%, #ebe6dd 75%, #e6e1d6 100%);
            color: #3d3d3d;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Elegant Background */
        .elegant-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 20%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(160, 82, 45, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(210, 180, 140, 0.04) 0%, transparent 50%);
        }

        /* Soft Particles */
        .soft-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .soft-particle {
            position: absolute;
            background: radial-gradient(circle, rgba(139, 69, 19, 0.1), rgba(139, 69, 19, 0.02));
            border-radius: 50%;
            animation: softFloat 20s ease-in-out infinite;
        }

        .soft-particle:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .soft-particle:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 15%;
            animation-delay: 7s;
            background: radial-gradient(circle, rgba(160, 82, 45, 0.08), rgba(160, 82, 45, 0.02));
        }

        .soft-particle:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 25%;
            left: 25%;
            animation-delay: 14s;
            background: radial-gradient(circle, rgba(210, 180, 140, 0.1), rgba(210, 180, 140, 0.02));
        }

        @keyframes softFloat {
            0%, 100% { 
                transform: translateY(0px) scale(1);
                opacity: 0.3;
            }
            33% { 
                transform: translateY(-30px) scale(1.1);
                opacity: 0.6;
            }
            66% { 
                transform: translateY(30px) scale(0.9);
                opacity: 0.4;
            }
        }

        /* Elegant Glass */
        .elegant-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.4) 0%, 
                rgba(255, 255, 255, 0.2) 50%, 
                rgba(255, 255, 255, 0.1) 100%);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 85%;
            max-width: 1100px;
            padding: 20px 35px;
            transition: all 0.3s ease;
        }

        .nav-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-family: 'Crimson Text', serif;
            font-size: 1.8rem;
            font-weight: 600;
            color: #8b4513;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #d2b48c, #deb887);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #8b4513;
            box-shadow: 
                0 4px 20px rgba(139, 69, 19, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent 70%);
            animation: elegantGlow 4s ease-in-out infinite;
        }

        @keyframes elegantGlow {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 35px;
        }

        .nav-menu a {
            color: #5d4e37;
            text-decoration: none;
            font-weight: 500;
            padding: 12px 20px;
            border-radius: 20px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-menu a:hover {
            color: #8b4513;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        }

        /* Elegant Buttons */
        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.95rem;
            position: relative;
            overflow: hidden;
        }

        .btn-elegant {
            background: linear-gradient(135deg, #d2b48c, #deb887);
            color: #8b4513;
            box-shadow: 
                0 4px 20px rgba(139, 69, 19, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }

        .btn-elegant:hover {
            transform: translateY(-3px);
            box-shadow: 
                0 8px 30px rgba(139, 69, 19, 0.25),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }

        .btn-elegant::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s;
        }

        .btn-elegant:hover::before {
            left: 100%;
        }

        .btn-soft {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            color: #8b4513;
            border: 2px solid rgba(139, 69, 19, 0.2);
        }

        .btn-soft:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.15);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 0 40px;
            position: relative;
        }

        .hero-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .hero-text h1 {
            font-family: 'Crimson Text', serif;
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 600;
            margin-bottom: 25px;
            color: #8b4513;
            line-height: 1.2;
        }

        .hero-text .highlight {
            color: #a0522d;
            position: relative;
        }

        .hero-text .highlight::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #d2b48c, #deb887);
            border-radius: 2px;
        }

        .hero-text p {
            font-size: 1.2rem;
            margin-bottom: 35px;
            color: #5d4e37;
            line-height: 1.7;
            font-weight: 400;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .hero-image {
            width: 350px;
            height: 350px;
            background: linear-gradient(135deg, rgba(210, 180, 140, 0.3), rgba(222, 184, 135, 0.2));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: #8b4513;
            box-shadow: 
                0 20px 40px rgba(139, 69, 19, 0.1),
                inset 0 4px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .hero-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4), transparent 70%);
            animation: elegantShimmer 6s ease-in-out infinite;
        }

        @keyframes elegantShimmer {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        /* Features Section */
        .features {
            padding: 100px 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            font-family: 'Crimson Text', serif;
            text-align: center;
            font-size: 2.8rem;
            font-weight: 600;
            margin-bottom: 60px;
            color: #8b4513;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 35px;
        }

        .feature-card {
            padding: 40px 35px;
            text-align: center;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 
                0 20px 40px rgba(139, 69, 19, 0.1),
                0 0 0 1px rgba(139, 69, 19, 0.05);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #d2b48c, #deb887);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #8b4513;
            box-shadow: 
                0 8px 25px rgba(139, 69, 19, 0.15),
                inset 0 2px 0 rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4), transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover .feature-icon::before {
            opacity: 1;
        }

        .feature-card h3 {
            font-family: 'Crimson Text', serif;
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #8b4513;
            font-weight: 600;
        }

        .feature-card p {
            color: #5d4e37;
            line-height: 1.6;
            font-weight: 400;
        }

        /* Testimonial Section */
        .testimonial {
            padding: 80px 40px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            margin: 60px 0;
        }

        .testimonial-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        .testimonial-quote {
            font-family: 'Crimson Text', serif;
            font-size: 1.5rem;
            color: #8b4513;
            margin-bottom: 25px;
            font-style: italic;
            line-height: 1.6;
        }

        .testimonial-author {
            color: #5d4e37;
            font-weight: 500;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 30px;
            left: 30px;
            z-index: 1001;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(139, 69, 19, 0.2);
            border-radius: 20px;
            color: #8b4513;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: translateX(-5px);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.15);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }
            
            .hero-visual {
                order: -1;
            }
            
            .hero-image {
                width: 280px;
                height: 280px;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .navbar {
                width: 95%;
                padding: 15px 25px;
            }
            
            .hero {
                padding: 0 20px;
            }
            
            .features {
                padding: 80px 20px;
            }
            
            .testimonial {
                padding: 60px 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Elegant Background -->
    <div class="elegant-bg"></div>
    
    <!-- Soft Particles -->
    <div class="soft-particles">
        <div class="soft-particle"></div>
        <div class="soft-particle"></div>
        <div class="soft-particle"></div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← العودة بأناقة</a>

    <!-- Navigation -->
    <nav class="navbar elegant-glass">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">🌿</div>
                Energy.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">الميزات الأنيقة</a></li>
                <li><a href="#testimonial">التقييمات</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            <a href="#contact" class="btn btn-elegant">ابدأ بأناقة</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <div class="hero-text">
                <h1>الطاقة الذكية <span class="highlight">بأناقة لا مثيل لها</span></h1>
                <p>اكتشف عالم الطاقة المدعوم بالذكاء الاصطناعي مع لمسة من الأناقة والرقي، حيث تلتقي التكنولوجيا المتقدمة بالتصميم الناعم والجميل</p>
                <div class="hero-actions">
                    <a href="#features" class="btn btn-elegant">استكشف بأناقة</a>
                    <a href="#contact" class="btn btn-soft">تواصل معنا</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-image elegant-glass">
                    🌿
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">ميزات أنيقة وناعمة</h2>
        <div class="features-grid">
            <div class="feature-card elegant-glass">
                <div class="feature-icon">🌸</div>
                <h3>تصميم ناعم</h3>
                <p>واجهات مصممة بعناية فائقة مع لمسات ناعمة وألوان هادئة تريح العين وتوفر تجربة استخدام مريحة ومريحة</p>
            </div>
            <div class="feature-card elegant-glass">
                <div class="feature-icon">🍃</div>
                <h3>طبيعة ذكية</h3>
                <p>حلول طاقة مستوحاة من الطبيعة مع خوارزميات ذكية تحاكي الأنماط الطبيعية لتحقيق أقصى كفاءة</p>
            </div>
            <div class="feature-card elegant-glass">
                <div class="feature-icon">☁️</div>
                <h3>سحابة ناعمة</h3>
                <p>تخزين سحابي متقدم مع واجهات ناعمة كالسحاب توفر وصولاً سهلاً وآمناً لبياناتك من أي مكان</p>
            </div>
            <div class="feature-card elegant-glass">
                <div class="feature-icon">🌙</div>
                <h3>راحة ليلية</h3>
                <p>أنظمة تحكم ذكية تتكيف مع دورة النوم الطبيعية لتوفير بيئة مثالية للراحة والاسترخاء</p>
            </div>
            <div class="feature-card elegant-glass">
                <div class="feature-icon">🌺</div>
                <h3>جمال وظيفي</h3>
                <p>دمج مثالي بين الجمال والوظيفة مع تصاميم أنيقة لا تضحي بالأداء أو الكفاءة</p>
            </div>
            <div class="feature-card elegant-glass">
                <div class="feature-icon">🕊️</div>
                <h3>سلام وهدوء</h3>
                <p>تجربة هادئة ومريحة مع تقنيات متقدمة تعمل بصمت لتوفير بيئة مثالية للعمل والحياة</p>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="testimonial" id="testimonial">
        <div class="testimonial-container">
            <div class="testimonial-quote">
                "تجربة رائعة ومريحة، التصميم الأنيق والناعم جعل إدارة الطاقة متعة حقيقية. لم أعد أشعر بالتعقيد في التكنولوجيا"
            </div>
            <div class="testimonial-author">
                - سارة أحمد، مديرة تنفيذية
            </div>
        </div>
    </section>

    <script>
        // Elegant Hover Effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2))';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.background = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Gentle Navbar Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2))';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))';
                navbar.style.backdropFilter = 'blur(15px)';
            }
        });

        // Gentle Animation Effects
        setInterval(() => {
            const particles = document.querySelectorAll('.soft-particle');
            particles.forEach(particle => {
                particle.style.opacity = Math.random() * 0.4 + 0.2;
            });
        }, 3000);

        console.log('🌸✨ Elegant Soft Demo Loaded Successfully! ✨🌸');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en" data-theme="ultra-modern">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Energy.AI - Ultra Modern Demo</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Ultra Modern Background */
        .ultra-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%, #0a0a0a 100%),
                radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 255, 0.05) 0%, transparent 50%);
        }

        /* Geometric Grid */
        .geometric-grid {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 100px 100px;
            animation: gridShift 20s linear infinite;
        }

        @keyframes gridShift {
            0% { transform: translate(0, 0); }
            100% { transform: translate(100px, 100px); }
        }

        /* Modern Elements */
        .modern-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .modern-element {
            position: absolute;
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
            animation: modernFloat 15s ease-in-out infinite;
        }

        .modern-element:nth-child(1) {
            width: 200px;
            height: 2px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .modern-element:nth-child(2) {
            width: 2px;
            height: 150px;
            top: 60%;
            right: 20%;
            animation-delay: 5s;
        }

        .modern-element:nth-child(3) {
            width: 100px;
            height: 100px;
            border: 2px solid rgba(0, 255, 255, 0.2);
            background: transparent;
            bottom: 25%;
            left: 25%;
            animation-delay: 10s;
        }

        .modern-element:nth-child(4) {
            width: 0;
            height: 0;
            border-left: 50px solid transparent;
            border-right: 50px solid transparent;
            border-bottom: 100px solid rgba(255, 0, 255, 0.1);
            top: 35%;
            right: 35%;
            animation-delay: 7s;
        }

        @keyframes modernFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% { 
                transform: translateY(-50px) rotate(180deg);
                opacity: 0.8;
            }
        }

        /* Ultra Glass */
        .ultra-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.05) 0%, 
                rgba(0, 0, 0, 0.2) 50%, 
                rgba(255, 255, 255, 0.02) 100%);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 20px 40px;
            transition: all 0.3s ease;
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: #00ffff;
            display: flex;
            align-items: center;
            gap: 12px;
            font-family: 'JetBrains Mono', monospace;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #000;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: ultraShine 2s ease-in-out infinite;
        }

        @keyframes ultraShine {
            0%, 100% { left: -100%; }
            50% { left: 100%; }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 40px;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #00ffff, #ff00ff);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover::before {
            width: 100%;
        }

        .nav-menu a:hover {
            color: #00ffff;
        }

        /* Ultra Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .btn-ultra {
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            color: #000;
            font-weight: 700;
        }

        .btn-ultra:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 255, 0.3);
        }

        .btn-ultra::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-ultra:hover::before {
            left: 100%;
        }

        .btn-outline-ultra {
            background: transparent;
            color: #00ffff;
            border: 2px solid #00ffff;
        }

        .btn-outline-ultra:hover {
            background: #00ffff;
            color: #000;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 0 40px;
            position: relative;
        }

        .hero-content {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .hero-text h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.1;
            color: #ffffff;
        }

        .hero-text .highlight-cyan {
            color: #00ffff;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .hero-text .highlight-magenta {
            color: #ff00ff;
            text-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
        }

        .hero-text p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .hero-actions {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .hero-graphic {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            position: relative;
            overflow: hidden;
        }

        .hero-graphic::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                transparent 30%, 
                rgba(0, 255, 255, 0.1) 50%, 
                transparent 70%);
            animation: ultraScan 3s ease-in-out infinite;
        }

        @keyframes ultraScan {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        /* Features Section */
        .features {
            padding: 100px 40px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 60px;
            color: #ffffff;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }

        .feature-card {
            padding: 40px 30px;
            text-align: left;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
            border-left: 3px solid transparent;
        }

        .feature-card:hover {
            transform: translateX(10px);
            border-left-color: #00ffff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #000;
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #00ffff;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .feature-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            font-size: 0.95rem;
        }

        /* Stats Section */
        .stats {
            padding: 80px 40px;
            background: rgba(0, 0, 0, 0.5);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: #00ffff;
            margin-bottom: 10px;
            font-family: 'JetBrains Mono', monospace;
        }

        .stat-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Back Button */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            padding: 10px 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            color: #00ffff;
            text-decoration: none;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(0, 255, 255, 0.1);
            transform: translateX(-5px);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }
            
            .hero-visual {
                order: -1;
            }
            
            .hero-graphic {
                width: 300px;
                height: 300px;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .navbar {
                padding: 15px 20px;
            }
            
            .hero {
                padding: 0 20px;
            }
            
            .features {
                padding: 80px 20px;
            }
            
            .stats {
                padding: 60px 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Ultra Modern Background -->
    <div class="ultra-bg"></div>
    <div class="geometric-grid"></div>
    
    <!-- Modern Elements -->
    <div class="modern-elements">
        <div class="modern-element"></div>
        <div class="modern-element"></div>
        <div class="modern-element"></div>
        <div class="modern-element"></div>
    </div>

    <!-- Back Button -->
    <a href="ui-preview-index.html" class="back-btn">← BACK TO GALLERY</a>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-content">
            <div class="logo">
                <div class="logo-icon">⚡</div>
                ENERGY.AI
            </div>
            <ul class="nav-menu">
                <li><a href="#home">HOME</a></li>
                <li><a href="#features">FEATURES</a></li>
                <li><a href="#stats">STATS</a></li>
                <li><a href="#contact">CONTACT</a></li>
            </ul>
            <a href="#contact" class="btn btn-ultra">GET STARTED</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-content">
            <div class="hero-text">
                <h1>
                    <span class="highlight-cyan">ULTRA</span> MODERN<br>
                    ENERGY <span class="highlight-magenta">SOLUTIONS</span>
                </h1>
                <p>Experience the future of energy management with cutting-edge technology, ultra-modern interfaces, and revolutionary AI-powered systems designed for the next generation.</p>
                <div class="hero-actions">
                    <a href="#features" class="btn btn-ultra">EXPLORE FEATURES</a>
                    <a href="#contact" class="btn btn-outline-ultra">START PROJECT</a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-graphic ultra-glass">
                    ⚡
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <h2 class="section-title">ULTRA FEATURES</h2>
        <div class="features-grid">
            <div class="feature-card ultra-glass">
                <div class="feature-icon">🚀</div>
                <h3>HYPER SPEED</h3>
                <p>Lightning-fast processing with quantum-enhanced algorithms that deliver real-time energy optimization at unprecedented speeds.</p>
            </div>
            <div class="feature-card ultra-glass">
                <div class="feature-icon">🧠</div>
                <h3>AI NEURAL CORE</h3>
                <p>Advanced neural networks powered by next-generation AI that learns, adapts, and evolves with your energy consumption patterns.</p>
            </div>
            <div class="feature-card ultra-glass">
                <div class="feature-icon">🔮</div>
                <h3>PREDICTIVE MATRIX</h3>
                <p>Future-ready prediction systems that anticipate energy needs and optimize consumption before you even realize it.</p>
            </div>
            <div class="feature-card ultra-glass">
                <div class="feature-icon">🛡️</div>
                <h3>QUANTUM SECURITY</h3>
                <p>Military-grade quantum encryption and multi-dimensional security protocols that protect your energy infrastructure.</p>
            </div>
            <div class="feature-card ultra-glass">
                <div class="feature-icon">📊</div>
                <h3>HOLOGRAPHIC DATA</h3>
                <p>Immersive 3D data visualization that transforms complex energy metrics into intuitive, interactive experiences.</p>
            </div>
            <div class="feature-card ultra-glass">
                <div class="feature-icon">🌐</div>
                <h3>GLOBAL SYNC</h3>
                <p>Seamless integration with worldwide energy grids and IoT ecosystems for comprehensive global energy management.</p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats" id="stats">
        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-number">99.9%</div>
                <div class="stat-label">UPTIME</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">50%</div>
                <div class="stat-label">ENERGY SAVINGS</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1000+</div>
                <div class="stat-label">CLIENTS</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">SUPPORT</div>
            </div>
        </div>
    </section>

    <script>
        // Ultra Modern Effects
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.background = 'linear-gradient(135deg, rgba(0, 255, 255, 0.05), rgba(255, 0, 255, 0.05))';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.background = '';
            });
        });

        // Smooth Scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Stats Animation
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = entry.target.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const finalValue = stat.textContent;
                        const numericValue = parseInt(finalValue.replace(/\D/g, ''));
                        const suffix = finalValue.replace(/[\d]/g, '');
                        
                        if (numericValue > 0) {
                            let currentValue = 0;
                            const increment = numericValue / 50;
                            
                            const timer = setInterval(() => {
                                currentValue += increment;
                                if (currentValue >= numericValue) {
                                    stat.textContent = finalValue;
                                    clearInterval(timer);
                                } else {
                                    stat.textContent = Math.floor(currentValue) + suffix;
                                }
                            }, 30);
                        }
                    });
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        const statsSection = document.querySelector('.stats');
        if (statsSection) {
            observer.observe(statsSection);
        }

        console.log('🚀⚡ Ultra Modern Demo Loaded Successfully! ⚡🚀');
    </script>
</body>
</html>

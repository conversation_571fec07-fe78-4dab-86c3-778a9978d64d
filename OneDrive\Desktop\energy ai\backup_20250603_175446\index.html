<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems.">
    <meta name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy">
    <meta name="author" content="Energy.AI">
    <meta name="robots" content="index, follow">
    <meta name="language" content="en, ar">
    <meta property="og:title" content="Energy.AI - Smart Energy Solutions">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <title>Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization</title>

    <!-- CSS Files -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/space-background.css">
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/naya.css">
    <link rel="stylesheet" href="css/enhanced-map-animations.css">
    <link rel="stylesheet" href="css/neon-cursor.css">


    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="main">
        <div class="navbar">
            <div class="icon">
                <div class="site-logo"></div>
                <h2 class="logo">Energy.Ai</h2>
            </div>

            <div class="menu">
                <ul>
                    <li><a href="#home" data-en="home">HOME</a></li>
                    <li><a href="#about" data-en="about">ABOUT</a></li>
                    <li><a href="#service" id="serviceLink" data-en="services">SERVICES</a></li>
                    <li><a href="#design" data-en="design">DESIGN</a></li>
                    <li><a href="#contact" data-en="contact">CONTACT</a></li>
                </ul>
            </div>
            <div class="language-toggle">
                <button class="lang-btn" data-lang="en">English</button>
                <button class="lang-btn" data-lang="ar">العربية</button>
            </div>
            <div class="theme-switch-wrapper">
                <label class="theme-switch" for="checkbox">
                    <input type="checkbox" id="checkbox" />
                    <div class="slider round"></div>
                </label>
            </div>
        </div>
        <div class="content" id="home">
            <h1>Web Design & <br><span>Development</span> <br>Energy</h1>
            <p class="par">AI is the spark igniting a new era of energy innovation<br> powering tomorrow with<br>intelligent solutions today</p>

            <button class="cn" id="joinBtn" data-en="join-us" data-ar="انضم إلينا">Join Us</button>

            <!-- Fixed CTA Button -->
            <div class="fixed-cta-container">
                <button class="fixed-cta-btn" id="fixedCtaBtn" data-en="get-free-consultation" data-ar="احصل على استشارة مجانية">
                    <ion-icon name="call-outline"></ion-icon>
                    <span>Get Free Consultation</span>
                </button>
            </div>





            <!-- AI Chat Interface -->
            <div class="ai-chat-container" id="aiChatContainer">
                <div class="chat-header">
                    <h3 data-en="Energy.AI Assistant">Energy.AI Assistant</h3>
                    <div class="chat-controls">
                        <button class="minimize-btn chat-control-btn" id="minimizeChatBtn">
                            <ion-icon name="remove-outline"></ion-icon>
                        </button>
                        <button class="close-btn chat-control-btn" id="closeChatBtn">
                            <ion-icon name="close-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <div class="message bot">
                        <div class="avatar">
                            <div class="avatar-icon-wrapper">
                                <ion-icon name="analytics-outline"></ion-icon>
                            </div>
                        </div>
                        <div class="message-content" data-en="Hello, I'm Energy.AI Assistant">Hello, I'm Energy.AI Assistant</div>
                    </div>
                </div>
                <div class="suggested-questions">
                    <button class="question-btn" data-en="How can AI improve energy efficiency?">How can AI improve energy efficiency?</button>
                    <button class="question-btn" data-en="What is renewable energy?">What is renewable energy?</button>
                    <button class="question-btn" data-en="How to reduce electricity bills?">How to reduce electricity bills?</button>
                </div>
                <div class="chat-input">
                    <input type="text" id="userInput" placeholder="Type your message here..." aria-label="Message input" data-en="Type your message here...">
                    <button id="sendAttachmentBtn" class="chat-icon-btn" aria-label="Send attachment">
                        <ion-icon name="attach-outline"></ion-icon>
                    </button>
                    <button id="sendMessageBtn" class="chat-send-btn">
                        <ion-icon name="send-outline"></ion-icon>
                        <span data-en="Send">Send</span>
                    </button>
                </div>
                <div class="typing-indicator" id="typingIndicator">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>

        <div id="about" class="section about-section">
            <h2>About Energy.AI</h2>
            <div class="about-content">
                <p>Energy.AI is at the forefront of combining artificial intelligence with energy solutions to create a sustainable future. Our innovative approaches help businesses and individuals optimize their energy usage, reduce costs, and minimize environmental impact.</p>
                <div class="features">
                    <div class="feature">
                        <div class="feature-icon-wrapper">
                            <ion-icon name="flash-outline"></ion-icon>
                        </div>
                        <h3>Smart Energy Management</h3>
                        <p>AI-powered solutions to optimize energy consumption in real-time.</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon-wrapper">
                            <ion-icon name="leaf-outline"></ion-icon>
                        </div>
                        <h3>Sustainable Solutions</h3>
                        <p>Eco-friendly approaches to energy production and distribution.</p>
                    </div>
                    <div class="feature">
                        <div class="feature-icon-wrapper">
                            <ion-icon name="analytics-outline"></ion-icon>
                        </div>
                        <h3>Data-Driven Insights</h3>
                        <p>Comprehensive analytics to make informed energy decisions.</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="service" class="section service-section">
            <h2>Our Services</h2>
            <div class="service-content">
                <div class="service-cards">
                    <div class="service-card">
                        <div class="service-icon">
                            <ion-icon name="bulb-outline"></ion-icon>
                        </div>
                        <h3>Energy Optimization</h3>
                        <p>Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <ion-icon name="trending-up-outline"></ion-icon>
                        </div>
                        <h3>Predictive Maintenance</h3>
                        <p>Prevent equipment failures before they happen with our predictive maintenance solutions powered by machine learning.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <ion-icon name="shield-outline"></ion-icon>
                        </div>
                        <h3>Energy Security</h3>
                        <p>Protect your energy infrastructure with advanced threat detection and response systems.</p>
                    </div>
                    <div class="service-card">
                        <div class="service-icon">
                            <ion-icon name="cloud-outline"></ion-icon>
                        </div>
                        <h3>Cloud Energy Management</h3>
                        <p>Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="design" class="section design-section">
            <h2>Our Design Approach</h2>
            <div class="design-gallery">
                <div class="design-card">
                    <div class="design-image">
                        <ion-icon name="home-outline"></ion-icon>
                    </div>
                    <h3>Smart Home Integration</h3>
                    <p>Seamless connection between your energy systems and smart home devices.</p>
                </div>

                <div class="design-card map-card">
                    <div class="design-image map-container">
                        <div id="embedded-map" class="embedded-map"></div>
                        <div class="map-overlay">
                            <h4>Energy.Ai Maps</h4>
                            <button class="map-expand-btn" id="expandMapBtn">
                                <ion-icon name="expand-outline"></ion-icon>
                                View Full Map
                            </button>
                        </div>
                    </div>
                    <h3>Energy.Ai Maps</h3>
                    <p>Interactive mapping interface with real-time energy data visualization and location-based analysis.</p>
                </div>
            </div>
        </div>

        <div id="contact" class="section contact-section">
            <div class="contact-header">
                <h2>Get In Touch</h2>
                <p class="contact-subtitle">Ready to transform your energy future? Let's start the conversation.</p>
            </div>

            <!-- Free Consultation Highlight Box -->
            <div class="consultation-highlight">
                <div class="consultation-content">
                    <div class="consultation-icon">
                        <ion-icon name="bulb-outline"></ion-icon>
                    </div>
                    <div class="consultation-text">
                        <h3>Get Your Free Energy Consultation</h3>
                        <p>Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.</p>
                        <ul class="consultation-benefits">
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Free energy audit and assessment</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Customized energy optimization plan</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>ROI analysis and cost savings projection</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="contact-container">
                <div class="contact-form-wrapper">
                    <div class="contact-form">
                        <h3>Send us a Message</h3>
                        <form id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        Full Name
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-company">
                                        <ion-icon name="business-outline"></ion-icon>
                                        Company
                                    </label>
                                    <input type="text" id="contact-company" placeholder="Your company name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        Email Address
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-phone">
                                        <ion-icon name="call-outline"></ion-icon>
                                        Phone Number
                                    </label>
                                    <input type="tel" id="contact-phone" placeholder="+962 XXX XXX XXX">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contact-subject">
                                    <ion-icon name="chatbubble-outline"></ion-icon>
                                    Subject
                                </label>
                                <select id="contact-subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="consultation" selected>Free Consultation</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="support">Technical Support</option>
                                    <option value="demo">Request Demo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="contact-message">
                                    <ion-icon name="document-text-outline"></ion-icon>
                                    Message
                                </label>
                                <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs and how we can help..." required></textarea>
                            </div>

                            <button type="submit" class="submit-btn" id="contactSubmitBtn">
                                <span class="btn-text">Send Message</span>
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                            <div id="contactFormStatus" class="form-status"></div>
                        </form>
                    </div>
                </div>

                <div class="contact-info-wrapper">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <p class="info-description">Connect with our energy experts today</p>

                        <div class="info-items">
                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="location-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Office Location</h4>
                                    <p>Amman, Jordan</p>
                                    <span>Middle East Headquarters</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <span>We reply within 24 hours</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="call-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Phone Number</h4>
                                    <p>+962 79 155 6430</p>
                                    <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="time-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Business Hours</h4>
                                    <p>Sunday - Thursday</p>
                                    <span>9:00 AM - 6:00 PM (GMT+3)</span>
                                </div>
                            </div>
                        </div>

                        <div class="contact-map">
                            <div class="map-placeholder">
                                <ion-icon name="map-outline"></ion-icon>
                                <p>Interactive Map</p>
                                <button class="map-btn" onclick="openContactMap()">View Location</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <h3>Energy.Ai Maps - Interactive View</h3>
                <button class="map-close-btn" id="closeMapModal">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div id="full-map" class="full-map"></div>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="footer-brand-icon"></div>
                <h2>Energy.Ai</h2>
                <p>Powering the future with intelligent solutions</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#service" id="footerServiceLink">Services</a></li>
                    <li><a href="#design">Design</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="faq.html">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><ion-icon name="logo-facebook"></ion-icon></a>
                    <a href="#" aria-label="Instagram"><ion-icon name="logo-instagram"></ion-icon></a>
                    <a href="#" aria-label="Twitter"><ion-icon name="logo-twitter"></ion-icon></a>
                </div>
            </div>
            <div class="footer-newsletter">
                <h3>Newsletter</h3>
                <p>Stay updated with our latest news</p>
                <div class="newsletter-form">
                    <input type="email" placeholder="Your Email Address" aria-label="Email for newsletter">
                    <button aria-label="Subscribe">
                        <ion-icon name="paper-plane-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Energy.AI. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <!-- JavaScript -->
    <script src="js/jarvis-config.js"></script>
    <script src="js/language-system.js"></script>
    <script src="js/auth-system.js"></script>
    <script src="js/embedded-map.js"></script>
    <script src="js/main.js"></script>

    <!-- Three.js and Neon Cursor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.139.2/build/three.module.js",
            "threejs-toys": "https://unpkg.com/threejs-toys@0.0.8/build/threejs-toys.module.cdn.min.js"
        }
    }
    </script>
    <script type="module" src="js/neon-cursor.js"></script>

    <!-- خلفية متحركة مع أشكال هندسية وتأثيرات متقدمة -->
    <div class="animated-background">
        <!-- الطبقة الأساسية للخلفية -->
        <div class="background-layer base-layer"></div>

        <!-- تأثير نبضة واضح -->
        <div class="pulse-effect"></div>

        <!-- الأشكال الهندسية الكبيرة -->
        <div class="geometric-shapes">
            <div class="main-geometric-shape"></div>
            <div class="secondary-shape"></div>
            <div class="tertiary-shape"></div>
            <div class="quaternary-shape"></div>
        </div>

        <!-- الأشكال المتحركة -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
            <div class="shape shape-7"></div>
            <div class="shape shape-8"></div>
        </div>

        <!-- الجسيمات العائمة المحسنة -->
        <div class="floating-particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
            <div class="particle particle-6"></div>
            <div class="particle particle-7"></div>
            <div class="particle particle-8"></div>
            <div class="particle particle-9"></div>
            <div class="particle particle-10"></div>
            <div class="particle particle-11"></div>
            <div class="particle particle-12"></div>
        </div>

        <!-- موجات الطاقة -->
        <div class="energy-waves">
            <div class="wave wave-1"></div>
            <div class="wave wave-2"></div>
            <div class="wave wave-3"></div>
        </div>

        <!-- النجوم المتلألئة -->
        <div class="twinkling-stars">
            <div class="star star-1"></div>
            <div class="star star-2"></div>
            <div class="star star-3"></div>
            <div class="star star-4"></div>
            <div class="star star-5"></div>
            <div class="star star-6"></div>
            <div class="star star-7"></div>
            <div class="star star-8"></div>
            <div class="star star-9"></div>
            <div class="star star-10"></div>
        </div>

        <!-- خطوط الطاقة المتحركة -->
        <div class="energy-lines">
            <div class="line line-1"></div>
            <div class="line line-2"></div>
            <div class="line line-3"></div>
            <div class="line line-4"></div>
        </div>



        <!-- خط طاقة واحد واضح مثل الصورة -->
        <div class="single-energy-line">
            <div class="main-energy-curve"></div>
        </div>
    </div>




    <!-- Load Naya Voice Assistant -->
    <script src="js/naya-assistant.js"></script>

</body>
</html>

/* brand-icons.css */

/* General styles for all brand icons */
.brand-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%; /* Make it circular */
    color: white; /* Icon color */
    text-decoration: none; /* Remove underlines from links */
    transition: background-color 0.3s ease; /* Smooth transition for hover effect */
    font-size: 1.2em; /* Adjust icon size as needed */
    width: 35px; /* Set a fixed width */
    height: 35px; /* Set a fixed height */
}

/* Hover effect */
.brand-icon:hover {
    opacity: 0.8; /* Slightly fade the icon on hover */
}

/* Specific brand styles */
.facebook {
    background-color: #1877F2;
}

.twitter {
    background-color: #1DA1F2;
}

.linkedin {
    background-color: #0077B5;
}

.github {
    background-color: #333;
}

.instagram {
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.youtube {
    background-color: #FF0000;
}

.medium {
    background-color: #00AB6C;
}

.devto {
    background-color: #0A0A0A;
}

.slack {
    background-color: #E01E5A;
}

/* Add more brand styles as needed */

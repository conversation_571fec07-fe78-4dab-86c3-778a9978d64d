/**
 * Energy.AI Backend Server
 * خادم محلي كامل مع قاعدة بيانات SQLite
 */

const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// إعداد التطبيق
const app = express();
const PORT = process.env.PORT || 5000;
const JWT_SECRET = process.env.JWT_SECRET || 'energy-ai-secret-key-2024';

// إعداد قاعدة البيانات SQLite
const dbPath = path.join(__dirname, 'energy-ai.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Error opening database:', err.message);
    } else {
        console.log('✅ Connected to SQLite database');
        initializeDatabase();
    }
});

// إعداد Google Gemini AI
const { GoogleGenerativeAI } = require('@google/generative-ai');
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || 'AIzaSyDplyiq89Ax8494spN6d7czLNnoloGaYQM');

// Middleware
app.use(helmet());
app.use(cors({
    origin: ['http://localhost:8000', 'http://127.0.0.1:8000', 'http://localhost:3000', 'http://127.0.0.1:3000', 'http://127.0.0.1:5500', 'file://', 'null'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api/', limiter);

// تهيئة قاعدة البيانات
function initializeDatabase() {
    // جدول المستخدمين
    db.run(`
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'moderator')),
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'banned')),
            avatar TEXT,
            phone TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME
        )
    `);

    // جدول المحادثات
    db.run(`
        CREATE TABLE IF NOT EXISTS conversations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            title TEXT DEFAULT 'محادثة جديدة',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `);

    // جدول الرسائل
    db.run(`
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            conversation_id INTEGER,
            role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
            content TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (conversation_id) REFERENCES conversations (id)
        )
    `);

    // جدول رسائل الاتصال
    db.run(`
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            message TEXT NOT NULL,
            status TEXT DEFAULT 'new' CHECK (status IN ('new', 'read', 'replied', 'archived')),
            admin_reply TEXT,
            replied_by INTEGER,
            replied_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (replied_by) REFERENCES users (id)
        )
    `);

    // جدول سجل النشاطات (للمراقبة)
    db.run(`
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            target_type TEXT,
            target_id INTEGER,
            details TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    `);

    // جدول الإعدادات العامة
    db.run(`
        CREATE TABLE IF NOT EXISTS site_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            setting_type TEXT DEFAULT 'text',
            description TEXT,
            updated_by INTEGER,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (updated_by) REFERENCES users (id)
        )
    `);

    // تحديث جدول المستخدمين إذا كان قديم
    setTimeout(() => {
        // التحقق من وجود عمود role
        db.get("PRAGMA table_info(users)", (err, info) => {
            if (err) {
                console.error('Error checking table structure:', err);
                return;
            }

            // إضافة الأعمدة المفقودة إذا لم تكن موجودة
            db.run("ALTER TABLE users ADD COLUMN role TEXT DEFAULT 'user'", (err) => {
                if (err && !err.message.includes('duplicate column')) {
                    console.error('Error adding role column:', err);
                }
            });

            db.run("ALTER TABLE users ADD COLUMN status TEXT DEFAULT 'active'", (err) => {
                if (err && !err.message.includes('duplicate column')) {
                    console.error('Error adding status column:', err);
                }
            });

            db.run("ALTER TABLE users ADD COLUMN avatar TEXT", (err) => {
                if (err && !err.message.includes('duplicate column')) {
                    console.error('Error adding avatar column:', err);
                }
            });

            db.run("ALTER TABLE users ADD COLUMN phone TEXT", (err) => {
                if (err && !err.message.includes('duplicate column')) {
                    console.error('Error adding phone column:', err);
                }
            });

            db.run("ALTER TABLE users ADD COLUMN last_login DATETIME", (err) => {
                if (err && !err.message.includes('duplicate column')) {
                    console.error('Error adding last_login column:', err);
                }

                // بعد إضافة الأعمدة، إنشاء المدير الافتراضي
                setTimeout(createDefaultAdmin, 500);
            });
        });
    }, 1000);

    function createDefaultAdmin() {
        db.get('SELECT id FROM users WHERE email = "<EMAIL>"', (err, row) => {
            if (err) {
                console.error('Error checking admin user:', err);
                return;
            }

            if (!row) {
                const adminPassword = bcrypt.hashSync('admin123', 10);

                db.run(
                    'INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, ?)',
                    ['مدير النظام', '<EMAIL>', adminPassword, 'admin', 'active'],
                    function(err) {
                        if (err) {
                            console.error('Error creating admin user:', err);
                        } else {
                            console.log('✅ Default admin user created: <EMAIL> / admin123');
                        }
                    }
                );
            } else {
                // تحديث المستخدم الموجود ليصبح مدير
                db.run(
                    'UPDATE users SET role = "admin", status = "active" WHERE email = "<EMAIL>"',
                    function(err) {
                        if (err) {
                            console.error('Error updating admin user:', err);
                        } else {
                            console.log('✅ Admin user updated: <EMAIL> / admin123');
                        }
                    }
                );
            }
        });
    }

    console.log('✅ Database tables initialized');
}

// Middleware للتحقق من المصادقة
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid token' });
        }
        req.user = user;
        next();
    });
}

// Middleware للتحقق من صلاحيات الإدارة
function requireAdmin(req, res, next) {
    if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
    }

    // التحقق من دور المستخدم في قاعدة البيانات
    db.get('SELECT role, status FROM users WHERE id = ?', [req.user.id], (err, user) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        if (user.status !== 'active') {
            return res.status(403).json({ error: 'Account is not active' });
        }

        if (user.role !== 'admin' && user.role !== 'moderator') {
            return res.status(403).json({ error: 'Admin privileges required' });
        }

        req.userRole = user.role;
        next();
    });
}

// Middleware لتسجيل النشاطات
function logActivity(action, targetType = null, targetId = null, details = null) {
    return (req, res, next) => {
        const userId = req.user ? req.user.id : null;
        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        db.run(
            'INSERT INTO activity_logs (user_id, action, target_type, target_id, details, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?, ?)',
            [userId, action, targetType, targetId, details, ipAddress, userAgent],
            (err) => {
                if (err) {
                    console.error('Error logging activity:', err);
                }
            }
        );

        next();
    };
}

// =====================
// مسارات المصادقة
// =====================

// تسجيل مستخدم جديد
app.post('/api/auth/register', async (req, res) => {
    try {
        const { name, email, password } = req.body;

        if (!name || !email || !password) {
            return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
        }

        // التحقق من وجود المستخدم
        db.get('SELECT id FROM users WHERE email = ?', [email], async (err, row) => {
            if (err) {
                return res.status(500).json({ error: 'خطأ في قاعدة البيانات' });
            }

            if (row) {
                return res.status(400).json({ error: 'البريد الإلكتروني مستخدم بالفعل' });
            }

            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(password, 10);

            // إدراج المستخدم الجديد
            db.run(
                'INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
                [name, email, hashedPassword],
                function(err) {
                    if (err) {
                        return res.status(500).json({ error: 'فشل في إنشاء الحساب' });
                    }

                    // إنشاء JWT token
                    const token = jwt.sign(
                        { id: this.lastID, email: email },
                        JWT_SECRET,
                        { expiresIn: '24h' }
                    );

                    res.status(201).json({
                        message: 'تم إنشاء الحساب بنجاح',
                        token: token,
                        user: {
                            id: this.lastID,
                            name: name,
                            email: email
                        }
                    });
                }
            );
        });
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'خطأ في الخادم' });
    }
});

// تسجيل الدخول
app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({ error: 'البريد الإلكتروني وكلمة المرور مطلوبان' });
        }

        // البحث عن المستخدم
        db.get('SELECT * FROM users WHERE email = ?', [email], async (err, user) => {
            if (err) {
                return res.status(500).json({ error: 'خطأ في قاعدة البيانات' });
            }

            if (!user) {
                return res.status(400).json({ error: 'بيانات الدخول غير صحيحة' });
            }

            // التحقق من كلمة المرور
            const validPassword = await bcrypt.compare(password, user.password);
            if (!validPassword) {
                return res.status(400).json({ error: 'بيانات الدخول غير صحيحة' });
            }

            // تحديث آخر تسجيل دخول
            db.run('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?', [user.id]);

            // إنشاء JWT token
            const token = jwt.sign(
                { id: user.id, email: user.email, role: user.role },
                JWT_SECRET,
                { expiresIn: '24h' }
            );

            res.json({
                message: 'تم تسجيل الدخول بنجاح',
                token: token,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    role: user.role,
                    status: user.status
                }
            });
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'خطأ في الخادم' });
    }
});

// الحصول على معلومات المستخدم
app.get('/api/auth/me', authenticateToken, (req, res) => {
    db.get('SELECT id, name, email, created_at FROM users WHERE id = ?', [req.user.id], (err, user) => {
        if (err) {
            return res.status(500).json({ error: 'خطأ في قاعدة البيانات' });
        }

        if (!user) {
            return res.status(404).json({ error: 'المستخدم غير موجود' });
        }

        res.json({ user });
    });
});

// =====================
// مسارات الدردشة
// =====================

// إرسال رسالة للذكاء الاصطناعي
app.post('/api/chat', async (req, res) => {
    try {
        const { messages, model = 'gemini-pro' } = req.body;

        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({ error: 'الرسائل مطلوبة' });
        }

        const lastMessage = messages[messages.length - 1];
        if (!lastMessage.content) {
            return res.status(400).json({ error: 'محتوى الرسالة مطلوب' });
        }

        // إعداد Gemini AI
        const model_instance = genAI.getGenerativeModel({ model: "gemini-pro" });

        // إنشاء prompt محسن للطاقة
        const energyPrompt = `أنت مساعد ذكي متخصص في مجال الطاقة المتجددة والاستدامة. اسمك "Energy.AI". 
        أجب على السؤال التالي بطريقة مفيدة ومفصلة، مع التركيز على الطاقة المتجددة والحلول المستدامة:

        السؤال: ${lastMessage.content}

        يرجى تقديم إجابة شاملة ومفيدة باللغة العربية.`;

        const result = await model_instance.generateContent(energyPrompt);
        const response = await result.response;
        const botReply = response.text();

        // حفظ المحادثة إذا كان المستخدم مسجل الدخول
        if (req.headers.authorization) {
            try {
                const token = req.headers.authorization.split(' ')[1];
                const decoded = jwt.verify(token, JWT_SECRET);
                
                // إنشاء محادثة جديدة أو استخدام موجودة
                db.run(
                    'INSERT INTO conversations (user_id, title) VALUES (?, ?)',
                    [decoded.id, lastMessage.content.substring(0, 50) + '...'],
                    function(err) {
                        if (!err) {
                            const conversationId = this.lastID;
                            
                            // حفظ رسالة المستخدم
                            db.run(
                                'INSERT INTO messages (conversation_id, role, content) VALUES (?, ?, ?)',
                                [conversationId, 'user', lastMessage.content]
                            );
                            
                            // حفظ رد الذكاء الاصطناعي
                            db.run(
                                'INSERT INTO messages (conversation_id, role, content) VALUES (?, ?, ?)',
                                [conversationId, 'assistant', botReply]
                            );
                        }
                    }
                );
            } catch (tokenError) {
                // تجاهل أخطاء التوكن - المحادثة ستعمل بدون حفظ
            }
        }

        // إرجاع الرد بتنسيق متوافق مع OpenAI
        res.json({
            choices: [{
                message: {
                    role: 'assistant',
                    content: botReply
                }
            }],
            model: 'gemini-pro',
            usage: {
                prompt_tokens: lastMessage.content.length,
                completion_tokens: botReply.length,
                total_tokens: lastMessage.content.length + botReply.length
            }
        });

    } catch (error) {
        console.error('Chat error:', error);
        res.status(500).json({ 
            error: 'عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.',
            details: error.message 
        });
    }
});

// =====================
// مسارات الاتصال
// =====================

// إرسال رسالة اتصال
app.post('/api/contact', (req, res) => {
    try {
        const { name, email, message } = req.body;

        if (!name || !email || !message) {
            return res.status(400).json({ error: 'جميع الحقول مطلوبة' });
        }

        db.run(
            'INSERT INTO contact_messages (name, email, message) VALUES (?, ?, ?)',
            [name, email, message],
            function(err) {
                if (err) {
                    console.error('Contact message error:', err);
                    return res.status(500).json({ error: 'فشل في إرسال الرسالة' });
                }

                res.json({
                    status: 'success',
                    message: 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
                });
            }
        );
    } catch (error) {
        console.error('Contact error:', error);
        res.status(500).json({ error: 'خطأ في الخادم' });
    }
});

// =====================
// مسارات الإدارة
// =====================

// لوحة تحكم الإدارة - إحصائيات عامة
app.get('/api/admin/dashboard', authenticateToken, requireAdmin, (req, res) => {
    const stats = {};

    // عدد المستخدمين
    db.get('SELECT COUNT(*) as total FROM users', (err, result) => {
        if (err) return res.status(500).json({ error: 'Database error' });
        stats.totalUsers = result.total;

        // عدد المستخدمين النشطين
        db.get('SELECT COUNT(*) as active FROM users WHERE status = "active"', (err, result) => {
            if (err) return res.status(500).json({ error: 'Database error' });
            stats.activeUsers = result.active;

            // عدد المحادثات
            db.get('SELECT COUNT(*) as total FROM conversations', (err, result) => {
                if (err) return res.status(500).json({ error: 'Database error' });
                stats.totalConversations = result.total;

                // عدد رسائل الاتصال الجديدة
                db.get('SELECT COUNT(*) as new FROM contact_messages WHERE status = "new"', (err, result) => {
                    if (err) return res.status(500).json({ error: 'Database error' });
                    stats.newMessages = result.new;

                    res.json({
                        message: 'Dashboard statistics',
                        stats: stats,
                        user: {
                            id: req.user.id,
                            role: req.userRole
                        }
                    });
                });
            });
        });
    });
});

// إدارة المستخدمين - عرض جميع المستخدمين
app.get('/api/admin/users', authenticateToken, requireAdmin, (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    db.all(
        'SELECT id, name, email, role, status, created_at, last_login FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?',
        [limit, offset],
        (err, users) => {
            if (err) {
                return res.status(500).json({ error: 'Database error' });
            }

            // عدد المستخدمين الإجمالي
            db.get('SELECT COUNT(*) as total FROM users', (err, countResult) => {
                if (err) {
                    return res.status(500).json({ error: 'Database error' });
                }

                res.json({
                    users: users,
                    pagination: {
                        page: page,
                        limit: limit,
                        total: countResult.total,
                        pages: Math.ceil(countResult.total / limit)
                    }
                });
            });
        }
    );
});

// تحديث حالة المستخدم
app.put('/api/admin/users/:id/status', authenticateToken, requireAdmin, logActivity('update_user_status', 'user'), (req, res) => {
    const userId = req.params.id;
    const { status } = req.body;

    if (!['active', 'inactive', 'banned'].includes(status)) {
        return res.status(400).json({ error: 'Invalid status' });
    }

    db.run(
        'UPDATE users SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, userId],
        function(err) {
            if (err) {
                return res.status(500).json({ error: 'Database error' });
            }

            if (this.changes === 0) {
                return res.status(404).json({ error: 'User not found' });
            }

            res.json({
                message: 'User status updated successfully',
                userId: userId,
                newStatus: status
            });
        }
    );
});

// تحديث دور المستخدم
app.put('/api/admin/users/:id/role', authenticateToken, requireAdmin, logActivity('update_user_role', 'user'), (req, res) => {
    const userId = req.params.id;
    const { role } = req.body;

    if (!['user', 'admin', 'moderator'].includes(role)) {
        return res.status(400).json({ error: 'Invalid role' });
    }

    // منع المستخدم من تغيير دوره الخاص
    if (parseInt(userId) === req.user.id) {
        return res.status(403).json({ error: 'Cannot change your own role' });
    }

    db.run(
        'UPDATE users SET role = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [role, userId],
        function(err) {
            if (err) {
                return res.status(500).json({ error: 'Database error' });
            }

            if (this.changes === 0) {
                return res.status(404).json({ error: 'User not found' });
            }

            res.json({
                message: 'User role updated successfully',
                userId: userId,
                newRole: role
            });
        }
    );
});

// إدارة رسائل الاتصال
app.get('/api/admin/messages', authenticateToken, requireAdmin, (req, res) => {
    const status = req.query.status || 'all';
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    let query = 'SELECT * FROM contact_messages';
    let params = [];

    if (status !== 'all') {
        query += ' WHERE status = ?';
        params.push(status);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);

    db.all(query, params, (err, messages) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }

        res.json({
            messages: messages,
            pagination: {
                page: page,
                limit: limit
            }
        });
    });
});

// تحديث حالة رسالة الاتصال
app.put('/api/admin/messages/:id/status', authenticateToken, requireAdmin, (req, res) => {
    const messageId = req.params.id;
    const { status, reply } = req.body;

    if (!['new', 'read', 'replied', 'archived'].includes(status)) {
        return res.status(400).json({ error: 'Invalid status' });
    }

    let updateQuery = 'UPDATE contact_messages SET status = ?';
    let params = [status];

    if (status === 'replied' && reply) {
        updateQuery += ', admin_reply = ?, replied_by = ?, replied_at = CURRENT_TIMESTAMP';
        params.push(reply, req.user.id);
    }

    updateQuery += ' WHERE id = ?';
    params.push(messageId);

    db.run(updateQuery, params, function(err) {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }

        if (this.changes === 0) {
            return res.status(404).json({ error: 'Message not found' });
        }

        res.json({
            message: 'Message status updated successfully',
            messageId: messageId,
            newStatus: status
        });
    });
});

// =====================
// مسارات عامة
// =====================

// الصفحة الرئيسية للAPI
app.get('/api', (req, res) => {
    res.json({
        message: 'Energy.AI Backend API',
        version: '1.0.0',
        status: 'running',
        endpoints: {
            auth: {
                register: 'POST /api/auth/register',
                login: 'POST /api/auth/login',
                me: 'GET /api/auth/me'
            },
            chat: 'POST /api/chat',
            contact: 'POST /api/contact'
        }
    });
});

// معالج الأخطاء العام
app.use((err, req, res, next) => {
    console.error('Server error:', err);
    res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

// معالج المسارات غير الموجودة
app.use((req, res) => {
    res.status(404).json({ error: 'المسار غير موجود' });
});

// تشغيل الخادم
app.listen(PORT, () => {
    console.log(`🚀 Energy.AI Backend Server running on http://localhost:${PORT}`);
    console.log(`📊 API Documentation: http://localhost:${PORT}/api`);
    console.log(`💾 Database: ${dbPath}`);
});

// إغلاق قاعدة البيانات عند إيقاف الخادم
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    db.close((err) => {
        if (err) {
            console.error('Error closing database:', err.message);
        } else {
            console.log('✅ Database connection closed');
        }
        process.exit(0);
    });
});
